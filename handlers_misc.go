package main

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func pullHandler(c *gin.Context) {
	var req PullRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Stream != nil && *req.Stream {
		setupSSEHeaders(c)

		total := int64(1000)
		for completed := int64(0); completed <= total; completed += 100 {
			response := PullResponse{
				Status:    "downloading model",
				Digest:    "sha256:fake789",
				Total:     total,
				Completed: completed,
			}
			if completed == total {
				response.Status = "success"
			}
			sendSSEResponse(c, response)
			time.Sleep(500 * time.Millisecond)
		}
		return
	}

	response := PullResponse{
		Status: "success",
		Digest: "sha256:fake789",
	}
	c.JSON(http.StatusOK, response)
}

func embedHandler(c *gin.Context) {
	var req EmbedRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert input to string array
	var inputs []string
	switch v := req.Input.(type) {
	case string:
		inputs = []string{v}
	case []interface{}:
		for _, item := range v {
			if str, ok := item.(string); ok {
				inputs = append(inputs, str)
			}
		}
	}

	// Generate fake embeddings
	embeddings := make([][]float32, len(inputs))
	for i := range inputs {
		embeddings[i] = make([]float32, 4096) // Assume 4096-dimensional vectors
		for j := range embeddings[i] {
			embeddings[i][j] = 0.1 // Set all dimensions to 0.1
		}
	}

	response := EmbedResponse{
		Model:      req.Model,
		Embeddings: embeddings,
	}

	c.JSON(http.StatusOK, response)
}

func createHandler(c *gin.Context) {
	var req CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Stream != nil && *req.Stream {
		setupSSEHeaders(c)

		steps := []string{
			"parsing modelfile",
			"creating model",
			"completed",
		}

		for _, step := range steps {
			sendSSEResponse(c, gin.H{
				"status": step,
				"digest": "sha256:fake789",
			})
			time.Sleep(500 * time.Millisecond)
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"digest": "sha256:fake789",
	})
}

func showHandler(c *gin.Context) {
	var req ShowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response := ShowResponse{
		License:    "MIT",
		System:     req.System,
		Template:   "{{ .Prompt }}",
		Parameters: "temperature 0.7\ntop_k 50\ntop_p 0.9",
		ModelInfo: map[string]any{
			"format":             "gguf",
			"family":             "llama",
			"families":           []string{"llama", "llama2"},
			"parameter_size":     "7B",
			"quantization_level": "Q4_K_M",
		},
		ModifiedAt: time.Now().UTC(),
	}

	c.JSON(http.StatusOK, response)
}

func copyHandler(c *gin.Context) {
	var req CopyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
	})
}

func deleteHandler(c *gin.Context) {
	var req DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
	})
}

func versionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"version": "0.5.7",
	})
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "Ollama is running")
}
