package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
)

// CLI configuration structure
type CLIConfig struct {
	Provider       string
	APIKey         string
	ConfigFile     string
	ModelsFile     string
	Port           string
	Host           string
	Verbose        bool
	DryRun         bool
	ShowVersion    bool
	SetAPIKey      string
	VerifyAPIKey   bool
	ListModels     bool
	ShowHelp       bool
	NonInteractive bool
	ShowConfig     bool
	ResetConfig    bool
	SaveConfig     bool
}

// Version information
const (
	Version     = "1.0.0"
	BuildDate   = "2025-01-22"
	Description = "OpenRouter proxy with Ollama API compatibility"
)

// Supported providers
var supportedProviders = map[string]string{
	"openrouter":      "OpenRouter (default) - Access to multiple AI models",
	"moonshot-direct": "Direct Moonshot AI API - Kimi K2 models",
	"anthropic":       "Direct Anthropic API - Claude models",
	"openai":          "Direct OpenAI API - GPT models",
}

// Parse command line arguments
func parseCLI() *CLIConfig {
	cli := &CLIConfig{}

	// Define flags
	flag.StringVar(&cli.Provider, "provider", "openrouter", "AI provider (openrouter, moonshot-direct, anthropic, openai)")
	flag.StringVar(&cli.APIKey, "api-key", "", "API key for the selected provider")
	flag.StringVar(&cli.ConfigFile, "config", "", "Path to configuration file")
	flag.StringVar(&cli.ModelsFile, "models", "models.json", "Path to model mappings file")
	flag.StringVar(&cli.Port, "port", "11434", "Server port")
	flag.StringVar(&cli.Host, "host", "0.0.0.0", "Server bind address")
	flag.BoolVar(&cli.Verbose, "verbose", false, "Enable verbose logging")
	flag.BoolVar(&cli.DryRun, "dry-run", false, "Validate configuration without starting server")
	flag.BoolVar(&cli.ShowVersion, "version", false, "Show version information")
	flag.StringVar(&cli.SetAPIKey, "set-api-key", "", "Securely store API key for provider")
	flag.BoolVar(&cli.VerifyAPIKey, "verify-api-key", false, "Test API key validity")
	flag.BoolVar(&cli.ListModels, "list-models", false, "Show available model mappings")
	flag.BoolVar(&cli.ShowHelp, "help", false, "Show help information")
	flag.BoolVar(&cli.NonInteractive, "non-interactive", false, "Disable interactive prompts for scripted usage")
	flag.BoolVar(&cli.ShowConfig, "show-config", false, "Show current saved configuration")
	flag.BoolVar(&cli.ResetConfig, "reset-config", false, "Reset saved configuration")
	flag.BoolVar(&cli.SaveConfig, "save-config", false, "Save current configuration")

	// Custom usage function
	flag.Usage = showUsage

	// Parse flags
	flag.Parse()

	return cli
}

// Show comprehensive usage information
func showUsage() {
	fmt.Printf(`%s v%s - %s

USAGE:
    fake-ollama [OPTIONS] [COMMAND]

COMMANDS:
    (default)                Start the proxy server
    --set-api-key <key>     Store API key securely
    --verify-api-key        Test API key validity
    --list-models           Show available model mappings
    --version               Show version information
    --help                  Show this help message
    --show-config           Show current saved configuration
    --reset-config          Reset saved configuration
    --save-config           Save current configuration

OPTIONS:
    --provider <name>       AI provider (default: openrouter)
                           Supported: %s
    
    --api-key <key>         API key for the provider
    --config <file>         Custom configuration file path
    --models <file>         Model mappings file (default: models.json)
    
    --host <address>        Server bind address (default: 0.0.0.0)
    --port <port>           Server port (default: 11434)
    
    --verbose               Enable detailed logging
    --dry-run               Validate configuration without starting server
    --non-interactive       Disable interactive prompts for scripted usage

EXAMPLES:
    # Start server with OpenRouter (default)
    fake-ollama --api-key sk-or-v1-your-openrouter-key-here

    # Use Moonshot AI directly for Kimi K2
    fake-ollama --provider moonshot-direct --api-key sk-your-moonshot-key-here

    # Store API key securely
    fake-ollama --set-api-key sk-or-v1-your-openrouter-key-here

    # Test configuration without starting
    fake-ollama --dry-run --verbose

    # Custom port and host
    fake-ollama --host 127.0.0.1 --port 8080

    # List available models
    fake-ollama --list-models

KIMI K2 INTEGRATION:
    # For VS Code native Ollama integration with Kimi K2:
    1. fake-ollama --provider openrouter --api-key your-openrouter-key
    2. Configure VS Code Ollama settings: http://localhost:11434
    3. Select model: kimi-k2:latest or kimi-k2:coding

ENVIRONMENT VARIABLES:
    OPENROUTER_API_KEY      OpenRouter API key
    MOONSHOT_API_KEY        Moonshot AI API key
    ANTHROPIC_API_KEY       Anthropic API key
    OPENAI_API_KEY          OpenAI API key
    OLLAMA_HOST             Server address (overrides --host and --port)

For more information, visit: https://github.com/spoonnotfound/fake-ollama
`, Description, Version, Description, strings.Join(getProviderList(), ", "))
}

// Get formatted list of providers
func getProviderList() []string {
	var providers []string
	for name, desc := range supportedProviders {
		providers = append(providers, fmt.Sprintf("%s (%s)", name, desc))
	}
	return providers
}

// Show version information
func showVersion() {
	fmt.Printf(`fake-ollama v%s
Build Date: %s
Description: %s

Supported Providers:
`, Version, BuildDate, Description)

	for name, desc := range supportedProviders {
		fmt.Printf("  %-15s %s\n", name, desc)
	}

	fmt.Printf(`
Features:
  ✓ Ollama API compatibility
  ✓ OpenAI API compatibility  
  ✓ Real AI model responses via OpenRouter
  ✓ Graceful fallback to mock responses
  ✓ Streaming support
  ✓ Model mapping configuration
  ✓ VS Code integration ready

Repository: https://github.com/spoonnotfound/fake-ollama
`)
}

// Validate CLI configuration
func (cli *CLIConfig) validate() error {
	// Validate provider
	if _, exists := supportedProviders[cli.Provider]; !exists {
		return fmt.Errorf("unsupported provider '%s'. Supported providers: %s", 
			cli.Provider, strings.Join(getProviderList(), ", "))
	}

	// Validate port
	if cli.Port != "" {
		if len(cli.Port) == 0 || cli.Port[0] == '-' {
			return fmt.Errorf("invalid port: %s", cli.Port)
		}
	}

	// Validate host
	if cli.Host == "" {
		return fmt.Errorf("host cannot be empty")
	}

	// Check if models file exists
	if cli.ModelsFile != "" {
		if _, err := os.Stat(cli.ModelsFile); os.IsNotExist(err) {
			return fmt.Errorf("models file not found: %s", cli.ModelsFile)
		}
	}

	// Check if config file exists (if specified)
	if cli.ConfigFile != "" {
		if _, err := os.Stat(cli.ConfigFile); os.IsNotExist(err) {
			return fmt.Errorf("config file not found: %s", cli.ConfigFile)
		}
	}

	return nil
}

// Get API key from CLI, environment, or config
func (cli *CLIConfig) getAPIKey() string {
	// Priority: CLI flag > environment variable > config file
	if cli.APIKey != "" {
		return cli.APIKey
	}

	// Check environment variables based on provider
	switch cli.Provider {
	case "openrouter":
		return os.Getenv("OPENROUTER_API_KEY")
	case "moonshot-direct":
		return os.Getenv("MOONSHOT_API_KEY")
	case "anthropic":
		return os.Getenv("ANTHROPIC_API_KEY")
	case "openai":
		return os.Getenv("OPENAI_API_KEY")
	}

	return ""
}

// Set up logging based on verbose flag
func (cli *CLIConfig) setupLogging() {
	if cli.Verbose {
		log.SetFlags(log.LstdFlags | log.Lshortfile)
		os.Setenv("LOG_LEVEL", "debug")
		os.Setenv("ENABLE_REQUEST_LOG", "true")
		os.Setenv("ENABLE_RESPONSE_LOG", "true")
	} else {
		log.SetFlags(log.LstdFlags)
		os.Setenv("LOG_LEVEL", "info")
	}
}

// Apply CLI configuration to environment and global config
func (cli *CLIConfig) apply() error {
	// Set up logging first
	cli.setupLogging()

	// Set API key environment variable
	apiKey := cli.getAPIKey()
	if apiKey != "" {
		switch cli.Provider {
		case "openrouter":
			os.Setenv("OPENROUTER_API_KEY", apiKey)
		case "moonshot-direct":
			os.Setenv("MOONSHOT_API_KEY", apiKey)
		case "anthropic":
			os.Setenv("ANTHROPIC_API_KEY", apiKey)
		case "openai":
			os.Setenv("OPENAI_API_KEY", apiKey)
		}
	}

	// Set server address
	if cli.Host != "" && cli.Port != "" {
		os.Setenv("OLLAMA_HOST", fmt.Sprintf("%s:%s", cli.Host, cli.Port))
	}

	// Set model mappings file
	if cli.ModelsFile != "" {
		os.Setenv("MODEL_MAPPING_FILE", cli.ModelsFile)
	}

	// Update global config if needed
	if appConfig != nil {
		appConfig.ModelMappingFile = cli.ModelsFile
		
		// Update provider-specific settings
		switch cli.Provider {
		case "moonshot-direct":
			appConfig.OpenRouterBaseURL = "https://api.moonshot.ai/v1"
		case "anthropic":
			appConfig.OpenRouterBaseURL = "https://api.anthropic.com/v1"
		case "openai":
			appConfig.OpenRouterBaseURL = "https://api.openai.com/v1"
		default:
			appConfig.OpenRouterBaseURL = "https://openrouter.ai/api/v1"
		}
	}

	return nil
}

// Process CLI commands and return whether to continue with server startup
func processCLI() bool {
	cli := parseCLI()

	// Handle help first
	if cli.ShowHelp {
		showUsage()
		return false
	}

	// Handle version
	if cli.ShowVersion {
		showVersion()
		return false
	}

	// Handle list models
	if cli.ListModels {
		return handleListModels(cli)
	}

	// Handle set API key
	if cli.SetAPIKey != "" {
		return handleSetAPIKey(cli)
	}

	// Handle verify API key
	if cli.VerifyAPIKey {
		return handleVerifyAPIKey(cli)
	}

	// Handle configuration commands
	if cli.ShowConfig {
		if err := handleShowConfig(cli); err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
		return true
	}

	if cli.ResetConfig {
		if err := handleResetConfig(cli); err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
		return true
	}

	if cli.SaveConfig {
		if err := handleSaveConfig(cli); err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
		return true
	}

	// Load persistent configuration before validation
	if err := loadAndApplyPersistentConfig(cli); err != nil {
		fmt.Fprintf(os.Stderr, "Warning: Failed to load saved configuration: %v\n", err)
	}

	// Validate configuration
	if err := cli.validate(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		fmt.Fprintf(os.Stderr, "Use --help for usage information\n")
		os.Exit(1)
	}

	// Check if interactive mode is needed
	needsInteractive := false
	if cli.getAPIKey() == "" && !cli.NonInteractive {
		needsInteractive = true
	}

	// Handle interactive mode if needed
	if needsInteractive {
		if err := handleInteractiveMode(cli); err != nil {
			// Check for special skip interactive signal
			if err.Error() == "SKIP_INTERACTIVE" {
				// User chose to use existing configuration, continue normally
			} else {
				showGracefulExit(err.Error())
				os.Exit(1)
			}
		}
	}

	// Apply configuration
	if err := cli.apply(); err != nil {
		fmt.Fprintf(os.Stderr, "Error applying configuration: %v\n", err)
		os.Exit(1)
	}

	// Handle dry run
	if cli.DryRun {
		return handleDryRun(cli)
	}

	// Continue with server startup
	return true
}

// Show startup information
func showStartupInfo(addr string) {
	fmt.Printf("\n")
	fmt.Printf("🚀 fake-ollama v%s\n", Version)
	fmt.Printf("📡 Server: http://%s\n", addr)

	// Show provider info
	provider := os.Getenv("PROVIDER")
	if provider == "" {
		provider = "openrouter"
	}

	baseURL := getProviderBaseURL(provider)
	fmt.Printf("🔗 Provider: %s (%s)\n", provider, baseURL)

	// Show model count
	if modelMappings != nil {
		fmt.Printf("🤖 Models: %d available\n", len(modelMappings))
	}

	// Show API key status
	apiKey := ""
	switch provider {
	case "openrouter":
		apiKey = os.Getenv("OPENROUTER_API_KEY")
	case "moonshot-direct":
		apiKey = os.Getenv("MOONSHOT_API_KEY")
	case "anthropic":
		apiKey = os.Getenv("ANTHROPIC_API_KEY")
	case "openai":
		apiKey = os.Getenv("OPENAI_API_KEY")
	}

	if apiKey != "" {
		fmt.Printf("🔑 API Key: Configured (%s...%s)\n", apiKey[:8], apiKey[len(apiKey)-4:])
	} else {
		fmt.Printf("⚠️  API Key: Not configured (will use fallback responses)\n")
	}

	fmt.Printf("\n")
	fmt.Printf("📖 Usage Examples:\n")
	fmt.Printf("   curl http://%s/api/tags\n", addr)
	fmt.Printf("   curl -X POST http://%s/api/chat -d '{\"model\":\"kimi-k2:latest\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}]}'\n", addr)
	fmt.Printf("\n")
	fmt.Printf("🔧 VS Code Integration:\n")
	fmt.Printf("   1. Configure VS Code's native Ollama settings\n")
	fmt.Printf("   2. Set Ollama endpoint: http://%s\n", addr)
	fmt.Printf("   3. Select model: kimi-k2:latest\n")
	fmt.Printf("\n")
}

// Load and apply persistent configuration to CLI config
func loadAndApplyPersistentConfig(cli *CLIConfig) error {
	// Only load if no explicit configuration is provided
	if cli.APIKey != "" || cli.Provider != "openrouter" {
		return nil // User provided explicit config, don't override
	}

	persistentConfig, err := loadPersistentConfig()
	if err != nil {
		return err
	}

	if persistentConfig != nil {
		fmt.Printf("📁 Using saved configuration: %s\n", getConfigSummary(persistentConfig))
		applyPersistentConfigToCLI(persistentConfig, cli)
	}

	return nil
}

// Handle show configuration command
func handleShowConfig(cli *CLIConfig) error {
	fmt.Printf("📋 Saved Configuration:\n")
	fmt.Printf("═══════════════════════\n\n")

	persistentConfig, err := loadPersistentConfig()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	if persistentConfig == nil {
		fmt.Printf("❌ No saved configuration found\n")
		fmt.Printf("💡 Run fake-ollama interactively to create a configuration\n\n")
		return nil
	}

	// Display configuration details
	fmt.Printf("✅ Configuration found:\n")
	fmt.Printf("   Provider: %s\n", persistentConfig.Provider)
	fmt.Printf("   API Key: %s...%s\n",
		persistentConfig.APIKey[:8],
		persistentConfig.APIKey[len(persistentConfig.APIKey)-4:])
	fmt.Printf("   Last Updated: %s\n", persistentConfig.LastUpdated.Format("2006-01-02 15:04:05"))

	if len(persistentConfig.ModelAPIKeys) > 0 {
		fmt.Printf("\n🤖 Model-specific API Keys:\n")
		for model, key := range persistentConfig.ModelAPIKeys {
			fmt.Printf("   %s: %s...%s\n", model, key[:8], key[len(key)-4:])
		}
	}

	if persistentConfig.ServerSettings.Host != "" || persistentConfig.ServerSettings.Port != "" {
		fmt.Printf("\n⚙️  Server Settings:\n")
		if persistentConfig.ServerSettings.Host != "" {
			fmt.Printf("   Host: %s\n", persistentConfig.ServerSettings.Host)
		}
		if persistentConfig.ServerSettings.Port != "" {
			fmt.Printf("   Port: %s\n", persistentConfig.ServerSettings.Port)
		}
	}

	configPath, _ := getConfigFilePath()
	fmt.Printf("\n📁 Config file: %s\n\n", configPath)

	return nil
}

// Handle reset configuration command
func handleResetConfig(cli *CLIConfig) error {
	fmt.Printf("🗑️  Reset Configuration:\n")
	fmt.Printf("═══════════════════════\n\n")

	if !hasPersistentConfig() {
		fmt.Printf("❌ No saved configuration found to reset\n\n")
		return nil
	}

	// Show current config before reset
	persistentConfig, err := loadPersistentConfig()
	if err == nil && persistentConfig != nil {
		fmt.Printf("Current configuration:\n")
		fmt.Printf("   %s\n\n", getConfigSummary(persistentConfig))
	}

	// Confirm reset
	fmt.Printf("⚠️  This will permanently delete your saved configuration.\n")
	fmt.Printf("Are you sure you want to continue? (y/N): ")

	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" && response != "yes" {
		fmt.Printf("❌ Reset cancelled\n\n")
		return nil
	}

	// Delete configuration
	if err := deletePersistentConfig(); err != nil {
		return fmt.Errorf("failed to reset configuration: %w", err)
	}

	fmt.Printf("✅ Configuration reset successfully\n")
	fmt.Printf("💡 Run fake-ollama interactively to create a new configuration\n\n")

	return nil
}

// Handle save configuration command
func handleSaveConfig(cli *CLIConfig) error {
	fmt.Printf("💾 Save Configuration:\n")
	fmt.Printf("═════════════════════\n\n")

	// Validate current CLI config
	if err := cli.validate(); err != nil {
		return fmt.Errorf("current configuration is invalid: %w", err)
	}

	// Create persistent config from CLI
	persistentConfig := createPersistentConfigFromCLI(cli)

	// Validate persistent config
	if err := validatePersistentConfig(persistentConfig); err != nil {
		return fmt.Errorf("failed to validate configuration: %w", err)
	}

	// Save configuration
	if err := savePersistentConfig(persistentConfig); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	fmt.Printf("✅ Configuration saved successfully\n")
	fmt.Printf("   %s\n", getConfigSummary(persistentConfig))

	configPath, _ := getConfigFilePath()
	fmt.Printf("📁 Saved to: %s\n\n", configPath)

	return nil
}
