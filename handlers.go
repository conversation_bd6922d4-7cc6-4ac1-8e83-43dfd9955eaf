package main

import (
	"fmt"
	"math/rand"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Check if a valid API key is configured
func hasValidAPIKey() bool {
	if appConfig == nil {
		return false
	}

	// Check for configured API key - fake-ollama requires external API access
	return appConfig.OpenRouterAPIKey != ""
}

// Create chat response helper function
func createChatResponse(id string, timestamp int64, model string, delta ChatCompletionDelta, finishReason *string) ChatResponse {
	return ChatResponse{
		ID:                id,
		Object:            "chat.completion.chunk",
		Created:           timestamp,
		Model:             model,
		SystemFingerprint: "fp_ollama",
		Choices: []ChatCompletionChoice{
			{
				Index:        0,
				Delta:        delta,
				FinishReason: finishReason,
			},
		},
	}
}

// Send thinking process for chat completions
func sendThinkingProcess(c *gin.Context, id string, timestamp int64, model, thinkText string, delay time.Duration) {
	// Send start marker
	startResp := createChatResponse(id, timestamp, model,
		ChatCompletionDelta{Role: "assistant", Content: "<think>"}, nil)
	sendSSEResponse(c, startResp)
	time.Sleep(delay)

	// Send newline
	newlineResp := createChatResponse(id, timestamp, model,
		ChatCompletionDelta{Content: "\n"}, nil)
	sendSSEResponse(c, newlineResp)
	time.Sleep(delay)

	// Send thinking content 3 times
	for i := 0; i < 3; i++ {
		chunks := splitIntoChunks(thinkText)
		for _, chunk := range chunks {
			resp := createChatResponse(id, timestamp, model,
				ChatCompletionDelta{Content: chunk}, nil)
			sendSSEResponse(c, resp)
			time.Sleep(delay)
		}
	}

	// Send end marker
	endResp := createChatResponse(id, timestamp, model,
		ChatCompletionDelta{Content: "</think>"}, nil)
	sendSSEResponse(c, endResp)
	time.Sleep(delay)

	// Send double newline
	doubleNewlineResp := createChatResponse(id, timestamp, model,
		ChatCompletionDelta{Content: "\n\n"}, nil)
	sendSSEResponse(c, doubleNewlineResp)
	time.Sleep(delay)
}

func chatHandler(c *gin.Context) {
	var req ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, err.Error()))
		return
	}

	// Validate model
	if !isValidModel(req.Model) {
		c.JSON(http.StatusNotFound, NewError(
			http.StatusNotFound,
			fmt.Sprintf("model %q not found, try pulling it first", req.Model),
		))
		return
	}

	// Validate messages
	if len(req.Messages) == 0 {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, "messages array is required"))
		return
	}

	lastMsg := req.Messages[len(req.Messages)-1]
	if lastMsg.Role == "" || lastMsg.Content == "" {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, "invalid message format"))
		return
	}

	thinkText, responseText := getResponseTexts(lastMsg.Content)
	delay := getModelDelay(req.Model)

	if req.Stream != nil && *req.Stream {
		setupSSEHeaders(c)

		id := fmt.Sprintf("chatcmpl-%d", rand.Intn(999))
		timestamp := time.Now().Unix()

		// Send thinking process
		sendThinkingProcess(c, id, timestamp, req.Model, thinkText, delay)

		// Send response content
		chunks := splitIntoChunks(responseText)
		for i, chunk := range chunks {
			select {
			case <-c.Request.Context().Done():
				return
			default:
				var finishReason *string
				if i == len(chunks)-1 {
					s := "stop"
					finishReason = &s
				}
				resp := createChatResponse(id, timestamp, req.Model,
					ChatCompletionDelta{Content: chunk}, finishReason)
				sendSSEResponse(c, resp)
				time.Sleep(delay)
			}
		}
		return
	}

	// Non-streaming request
	response := createChatResponse(fmt.Sprintf("chatcmpl-%d", time.Now().Unix()), time.Now().Unix(), req.Model, ChatCompletionDelta{
		Role:    "assistant",
		Content: responseText,
	}, func() *string { s := "stop"; return &s }())

	c.JSON(http.StatusOK, response)
}

func generateHandler(c *gin.Context) {
	var req GenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, err.Error()))
		return
	}

	// Validate model
	if !isValidModel(req.Model) {
		c.JSON(http.StatusNotFound, NewError(
			http.StatusNotFound,
			fmt.Sprintf("model %q not found, try pulling it first", req.Model),
		))
		return
	}

	if req.Prompt == "" {
		// If no prompt, return load success response
		c.JSON(http.StatusOK, GenerateResponse{
			Model:      req.Model,
			CreatedAt:  time.Now().UTC(),
			Done:       true,
			DoneReason: "load",
		})
		return
	}

	// Determine language based on input content
	isChinese := containsEnglish(req.Prompt)

	var responseText string
	if isChinese {
		responseText = ChineseResponseText
	} else {
		responseText = EnglishResponseText
	}

	startTime := time.Now()
	loadDuration := 100 * time.Millisecond // Simulate load time

	// Set delay based on model size
	delay := getModelDelay(req.Model)

	if req.Stream != nil && *req.Stream {
		setupSSEHeaders(c)

		chunks := splitIntoChunks(responseText)
		for i, chunk := range chunks {
			select {
			case <-c.Request.Context().Done():
				return
			default:
				partialResponse := GenerateResponse{
					Model:     req.Model,
					CreatedAt: time.Now().UTC(),
					Response:  chunk,
					Done:      i == len(chunks)-1,
				}

				if i == len(chunks)-1 {
					partialResponse.TotalDuration = time.Since(startTime)
					partialResponse.LoadDuration = loadDuration
					partialResponse.PromptEvalCount = 10
					partialResponse.EvalCount = 20
					partialResponse.Context = []int{1, 2, 3}
				}

				sendSSEResponse(c, partialResponse)
				time.Sleep(delay)
			}
		}
		return
	}

	response := GenerateResponse{
		Model:           req.Model,
		CreatedAt:       time.Now().UTC(),
		Response:        responseText,
		Done:            true,
		Context:         []int{1, 2, 3}, // Simulate context tokens
		TotalDuration:   time.Since(startTime),
		LoadDuration:    loadDuration,
		PromptEvalCount: 10, // Simulate eval count
		EvalCount:       20,
	}

	c.JSON(http.StatusOK, response)
}

func ListHandler(c *gin.Context) {
	// Check if client connection is already closed
	if c.Writer.Written() {
		return
	}

	// Enforce API key requirement - no local models available
	if !hasValidAPIKey() {
		// Return empty model list with helpful message
		c.JSON(http.StatusOK, ListResponse{
			Models:  []ListModelResponse{},
			Message: "No models available - API key required. fake-ollama is a proxy service with no local models. Configure an API key to access AI models through supported providers.",
		})
		return
	}

	// Create model list only if API key is configured
	models := createModelList()

	// Set response headers
	c.Header("Content-Type", "application/json")
	c.Header("Connection", "keep-alive")

	select {
	case <-c.Request.Context().Done():
		// Client disconnected
		return
	default:
		// Return model list (no message when models are available)
		c.JSON(http.StatusOK, ListResponse{
			Models: models,
		})
	}
}
