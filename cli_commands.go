package main

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Handle --list-models command
func handleListModels(cli *CLIConfig) bool {
	fmt.Printf("Loading model mappings from: %s\n\n", cli.ModelsFile)

	// Load model mappings
	mappings, err := loadModelMappings(cli.ModelsFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error loading model mappings: %v\n", err)
		os.Exit(1)
	}

	if len(mappings) == 0 {
		fmt.Println("No model mappings found.")
		return false
	}

	// Group by provider
	providerGroups := make(map[string][]ModelMapping)
	for _, mapping := range mappings {
		providerGroups[mapping.Provider] = append(providerGroups[mapping.Provider], mapping)
	}

	fmt.Printf("Available Models (%d total):\n", len(mappings))
	fmt.Println(strings.Repeat("=", 80))

	for provider, models := range providerGroups {
		fmt.Printf("\n%s Provider:\n", strings.ToUpper(provider))
		fmt.Println(strings.Repeat("-", 40))

		for _, model := range models {
			fmt.Printf("  %-20s → %-30s", model.OllamaName, model.OpenRouterName)
			
			// Add context length info
			if model.ContextLength > 0 {
				fmt.Printf(" (%dk context)", model.ContextLength/1000)
			}
			
			// Add capabilities
			if len(model.Capabilities) > 0 {
				fmt.Printf("\n  %20s   Capabilities: %s", "", strings.Join(model.Capabilities, ", "))
			}
			
			// Add description
			if model.Description != "" {
				fmt.Printf("\n  %20s   %s", "", model.Description)
			}
			
			fmt.Println()
		}
	}

	fmt.Printf("\nUsage Examples:\n")
	fmt.Println("  # Use Kimi K2 for coding")
	fmt.Println("  curl -X POST http://localhost:11434/api/chat \\")
	fmt.Println("    -d '{\"model\": \"kimi-k2:coding\", \"messages\": [{\"role\": \"user\", \"content\": \"Write a Python function\"}]}'")
	fmt.Println()
	fmt.Println("  # Use DeepSeek R1 for reasoning")
	fmt.Println("  curl -X POST http://localhost:11434/api/chat \\")
	fmt.Println("    -d '{\"model\": \"deepseek-r1:14b\", \"messages\": [{\"role\": \"user\", \"content\": \"Explain quantum computing\"}]}'")

	return false
}

// Handle --set-api-key command
func handleSetAPIKey(cli *CLIConfig) bool {
	if cli.SetAPIKey == "" {
		fmt.Fprintf(os.Stderr, "Error: API key cannot be empty\n")
		os.Exit(1)
	}

	// Validate API key format based on provider
	if err := validateAPIKeyFormat(cli.Provider, cli.SetAPIKey); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}

	// Store API key in environment variable
	envVar := getAPIKeyEnvVar(cli.Provider)
	
	// Create or update shell profile
	if err := storeAPIKeyInProfile(envVar, cli.SetAPIKey); err != nil {
		fmt.Fprintf(os.Stderr, "Error storing API key: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✓ API key for %s provider stored successfully\n", cli.Provider)
	fmt.Printf("✓ Added %s to your shell profile\n", envVar)
	fmt.Println()
	fmt.Println("To use the API key immediately, run:")
	fmt.Printf("  export %s='%s'\n", envVar, cli.SetAPIKey)
	fmt.Println()
	fmt.Println("Or restart your terminal to load from profile.")

	return false
}

// Handle --verify-api-key command
func handleVerifyAPIKey(cli *CLIConfig) bool {
	apiKey := cli.getAPIKey()
	if apiKey == "" {
		fmt.Fprintf(os.Stderr, "Error: No API key found. Use --api-key flag or set environment variable.\n")
		fmt.Fprintf(os.Stderr, "Environment variable for %s: %s\n", cli.Provider, getAPIKeyEnvVar(cli.Provider))
		os.Exit(1)
	}

	// Validate API key format first
	if err := validateAPIKeyFormat(cli.Provider, apiKey); err != nil {
		fmt.Printf("✗ API key format validation failed: %v\n", err)
		fmt.Println()
		fmt.Println("Troubleshooting:")
		fmt.Printf("  1. Check your API key format for %s\n", cli.Provider)
		fmt.Printf("  2. Ensure you copied the complete key without extra spaces\n")
		fmt.Printf("  3. Expected format: %s\n", getAPIKeyFormat(cli.Provider))
		os.Exit(1)
	}

	fmt.Printf("Verifying API key for %s provider...\n", cli.Provider)

	// Create temporary config for testing
	testConfig := &Config{
		OpenRouterAPIKey:  apiKey,
		OpenRouterBaseURL: getProviderBaseURL(cli.Provider),
		RequestTimeout:    10,
		MaxRetries:        1,
		EnableRequestLog:  cli.Verbose,
		EnableResponseLog: cli.Verbose,
	}

	// Test API key
	if err := testAPIKey(testConfig, cli.Provider); err != nil {
		fmt.Printf("✗ API key verification failed: %v\n", err)
		fmt.Println()
		fmt.Println("Troubleshooting:")
		fmt.Printf("  1. Check your API key is valid for %s\n", cli.Provider)
		fmt.Printf("  2. Ensure you have sufficient credits/quota\n")
		fmt.Printf("  3. Verify network connectivity\n")
		os.Exit(1)
	}

	fmt.Printf("✓ API key verified successfully for %s\n", cli.Provider)
	fmt.Println("✓ Ready to start fake-ollama server")

	return false
}

// Handle --dry-run command
func handleDryRun(cli *CLIConfig) bool {
	fmt.Println("Dry Run - Configuration Validation")
	fmt.Println(strings.Repeat("=", 50))

	// Check API key
	apiKey := cli.getAPIKey()
	if apiKey == "" {
		fmt.Printf("⚠ Warning: No API key found for %s provider\n", cli.Provider)
		fmt.Printf("  Set environment variable: %s\n", getAPIKeyEnvVar(cli.Provider))
	} else {
		fmt.Printf("✓ API key found for %s provider\n", cli.Provider)
	}

	// Check model mappings
	mappings, err := loadModelMappings(cli.ModelsFile)
	if err != nil {
		fmt.Printf("✗ Error loading model mappings: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("✓ Model mappings loaded: %d models from %s\n", len(mappings), cli.ModelsFile)

	// Check server configuration
	fmt.Printf("✓ Server will bind to: %s:%s\n", cli.Host, cli.Port)

	// Check provider configuration
	baseURL := getProviderBaseURL(cli.Provider)
	fmt.Printf("✓ Provider: %s (%s)\n", cli.Provider, baseURL)

	// Show configuration summary
	fmt.Println()
	fmt.Println("Configuration Summary:")
	fmt.Printf("  Provider:     %s\n", cli.Provider)
	fmt.Printf("  Base URL:     %s\n", baseURL)
	fmt.Printf("  Models File:  %s\n", cli.ModelsFile)
	fmt.Printf("  Server:       %s:%s\n", cli.Host, cli.Port)
	fmt.Printf("  Verbose:      %v\n", cli.Verbose)

	if apiKey != "" {
		fmt.Printf("  API Key:      %s...%s\n", apiKey[:8], apiKey[len(apiKey)-4:])
	}

	fmt.Println()
	fmt.Println("✓ Configuration is valid")
	fmt.Println("✓ Ready to start server (remove --dry-run flag)")

	return false
}

// Validate API key format
func validateAPIKeyFormat(provider, apiKey string) error {
	// Basic length check - real API keys are typically 40+ characters
	if len(apiKey) < 20 {
		return fmt.Errorf("API key too short (minimum 20 characters, got %d)", len(apiKey))
	}

	switch provider {
	case "openrouter":
		if !strings.HasPrefix(apiKey, "sk-or-v1-") {
			return fmt.Errorf("OpenRouter API keys should start with 'sk-or-v1-'")
		}
		// OpenRouter keys are typically 60+ characters
		if len(apiKey) < 50 {
			return fmt.Errorf("OpenRouter API key appears too short (expected 50+ characters, got %d)", len(apiKey))
		}
	case "openai":
		if !strings.HasPrefix(apiKey, "sk-") {
			return fmt.Errorf("OpenAI API keys should start with 'sk-'")
		}
		// OpenAI keys are typically 50+ characters
		if len(apiKey) < 40 {
			return fmt.Errorf("OpenAI API key appears too short (expected 40+ characters, got %d)", len(apiKey))
		}
	case "anthropic":
		if !strings.HasPrefix(apiKey, "sk-ant-") {
			return fmt.Errorf("Anthropic API keys should start with 'sk-ant-'")
		}
		// Anthropic keys are typically 80+ characters
		if len(apiKey) < 60 {
			return fmt.Errorf("Anthropic API key appears too short (expected 60+ characters, got %d)", len(apiKey))
		}
	case "moonshot-direct":
		// Moonshot keys can have various formats, just check reasonable length
		if len(apiKey) < 30 {
			return fmt.Errorf("Moonshot API key appears too short (expected 30+ characters, got %d)", len(apiKey))
		}
	}

	return nil
}

// Get environment variable name for provider
func getAPIKeyEnvVar(provider string) string {
	switch provider {
	case "openrouter":
		return "OPENROUTER_API_KEY"
	case "moonshot-direct":
		return "MOONSHOT_API_KEY"
	case "anthropic":
		return "ANTHROPIC_API_KEY"
	case "openai":
		return "OPENAI_API_KEY"
	default:
		return "API_KEY"
	}
}

// Get base URL for provider
func getProviderBaseURL(provider string) string {
	switch provider {
	case "openrouter":
		return "https://openrouter.ai/api/v1"
	case "moonshot-direct":
		return "https://api.moonshot.ai/v1"
	case "anthropic":
		return "https://api.anthropic.com/v1"
	case "openai":
		return "https://api.openai.com/v1"
	default:
		return "https://openrouter.ai/api/v1"
	}
}

// Store API key in shell profile
func storeAPIKeyInProfile(envVar, apiKey string) error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("cannot find home directory: %v", err)
	}

	// Try common shell profile files
	profileFiles := []string{
		filepath.Join(homeDir, ".bashrc"),
		filepath.Join(homeDir, ".zshrc"),
		filepath.Join(homeDir, ".profile"),
	}

	var targetFile string
	for _, file := range profileFiles {
		if _, err := os.Stat(file); err == nil {
			targetFile = file
			break
		}
	}

	// If no profile file exists, create .profile
	if targetFile == "" {
		targetFile = filepath.Join(homeDir, ".profile")
	}

	// Read existing content
	var content []byte
	if _, err := os.Stat(targetFile); err == nil {
		content, err = ioutil.ReadFile(targetFile)
		if err != nil {
			return fmt.Errorf("cannot read profile file: %v", err)
		}
	}

	// Check if environment variable already exists
	exportLine := fmt.Sprintf("export %s='%s'", envVar, apiKey)
	contentStr := string(content)

	// Remove existing line if present
	lines := strings.Split(contentStr, "\n")
	var newLines []string
	for _, line := range lines {
		if !strings.Contains(line, envVar+"=") && !strings.Contains(line, "export "+envVar) {
			newLines = append(newLines, line)
		}
	}

	// Add new export line
	newLines = append(newLines, "", "# fake-ollama API key", exportLine)

	// Write back to file
	newContent := strings.Join(newLines, "\n")
	return ioutil.WriteFile(targetFile, []byte(newContent), 0644)
}

// Test API key validity
func testAPIKey(config *Config, provider string) error {
	client := NewOpenRouterClient(config)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Try to get models list as a simple API test
	_, err := client.GetModels(ctx)
	return err
}
