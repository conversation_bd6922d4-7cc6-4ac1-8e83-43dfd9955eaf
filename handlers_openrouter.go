package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Global OpenRouter client instance
var openRouterClient *OpenRouterClient

// Initialize OpenRouter client
func initOpenRouterClient() {
	if appConfig != nil {
		openRouterClient = NewOpenRouterClient(appConfig)
		log.Printf("OpenRouter client initialized with base URL: %s", appConfig.OpenRouterBaseURL)
	}
}

// Enhanced chat handler with OpenRouter integration
func chatHandlerWithOpenRouter(c *gin.Context) {
	var req ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, err.Error()))
		return
	}

	// Validate model
	if !isValidModel(req.Model) {
		c.JSO<PERSON>(http.StatusNotFound, NewError(
			http.StatusNotFound,
			fmt.Sprintf("model %q not found, try pulling it first", req.Model),
		))
		return
	}

	// Validate messages
	if len(req.Messages) == 0 {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, "messages array is required"))
		return
	}

	lastMsg := req.Messages[len(req.Messages)-1]
	if lastMsg.Role == "" || lastMsg.Content == "" {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, "invalid message format"))
		return
	}

	// Get OpenRouter model mapping
	mapping, exists := modelMappings[req.Model]
	if !exists {
		// Fallback to mock response if mapping not found
		log.Printf("Model mapping not found for %s, falling back to mock response", req.Model)
		chatHandlerFallback(c, req)
		return
	}

	// Transform request to OpenRouter format
	openRouterReq := transformChatRequest(req, mapping.OpenRouterName)

	// Handle streaming vs non-streaming
	if req.Stream != nil && *req.Stream {
		handleStreamingChatCompletion(c, openRouterReq, req.Model)
	} else {
		handleNonStreamingChatCompletion(c, openRouterReq, req.Model)
	}
}

// Handle non-streaming chat completion
func handleNonStreamingChatCompletion(c *gin.Context, openRouterReq OpenRouterChatRequest, originalModel string) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), time.Duration(appConfig.RequestTimeout)*time.Second)
	defer cancel()

	// Call OpenRouter API
	response, err := openRouterClient.ChatCompletion(ctx, openRouterReq)
	if err != nil {
		log.Printf("OpenRouter API error: %v", err)
		// Fallback to mock response
		chatHandlerFallback(c, ChatRequest{
			Model:    originalModel,
			Messages: transformOpenRouterMessages(openRouterReq.Messages),
		})
		return
	}

	// Transform response back to Ollama format
	ollamaResponse := transformChatResponse(*response)
	
	// Override model name to match original request
	ollamaResponse.Model = originalModel

	c.JSON(http.StatusOK, ollamaResponse)
}

// Handle streaming chat completion
func handleStreamingChatCompletion(c *gin.Context, openRouterReq OpenRouterChatRequest, originalModel string) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), time.Duration(appConfig.RequestTimeout)*time.Second)
	defer cancel()

	setupSSEHeaders(c)

	// Call OpenRouter streaming API
	responseChan, errorChan := openRouterClient.ChatCompletionStream(ctx, openRouterReq)

	for {
		select {
		case <-c.Request.Context().Done():
			cancel()
			return
		case err := <-errorChan:
			if err != nil {
				log.Printf("OpenRouter streaming error: %v", err)
				// Send error as SSE and fallback
				sendSSEError(c, "OpenRouter API error, falling back to mock response")
				chatHandlerStreamingFallback(c, ChatRequest{
					Model:    originalModel,
					Messages: transformOpenRouterMessages(openRouterReq.Messages),
					Stream:   boolPtr(true),
				})
				return
			}
		case response, ok := <-responseChan:
			if !ok {
				// Stream ended normally
				return
			}
			
			// Transform and send response
			ollamaResponse := transformChatResponse(response)
			ollamaResponse.Model = originalModel
			
			sendSSEResponse(c, ollamaResponse)
		}
	}
}

// Enhanced generate handler with OpenRouter integration
func generateHandlerWithOpenRouter(c *gin.Context) {
	var req GenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewError(http.StatusBadRequest, err.Error()))
		return
	}

	// Validate model
	if !isValidModel(req.Model) {
		c.JSON(http.StatusNotFound, NewError(
			http.StatusNotFound,
			fmt.Sprintf("model %q not found, try pulling it first", req.Model),
		))
		return
	}

	if req.Prompt == "" {
		// If no prompt, return load success response
		c.JSON(http.StatusOK, GenerateResponse{
			Model:      req.Model,
			CreatedAt:  time.Now().UTC(),
			Done:       true,
			DoneReason: "load",
		})
		return
	}

	// Get OpenRouter model mapping
	mapping, exists := modelMappings[req.Model]
	if !exists {
		// Fallback to mock response if mapping not found
		log.Printf("Model mapping not found for %s, falling back to mock response", req.Model)
		generateHandlerFallback(c, req)
		return
	}

	// Transform request to OpenRouter format
	openRouterReq := transformGenerateRequest(req, mapping.OpenRouterName)

	ctx, cancel := context.WithTimeout(c.Request.Context(), time.Duration(appConfig.RequestTimeout)*time.Second)
	defer cancel()

	// Call OpenRouter API
	response, err := openRouterClient.TextCompletion(ctx, openRouterReq)
	if err != nil {
		log.Printf("OpenRouter API error: %v", err)
		// Fallback to mock response
		generateHandlerFallback(c, req)
		return
	}

	// Transform response back to Ollama format
	ollamaResponse := transformGenerateResponse(*response)
	
	// Override model name to match original request
	ollamaResponse.Model = req.Model

	c.JSON(http.StatusOK, ollamaResponse)
}

// Fallback handlers for when OpenRouter is unavailable
func chatHandlerFallback(c *gin.Context, req ChatRequest) {
	log.Printf("Using fallback mock response for chat request")
	
	lastMsg := req.Messages[len(req.Messages)-1]
	_, responseText := getResponseTexts(lastMsg.Content)
	
	if req.Stream != nil && *req.Stream {
		chatHandlerStreamingFallback(c, req)
		return
	}

	// Non-streaming fallback
	response := createChatResponse(fmt.Sprintf("chatcmpl-%d", time.Now().Unix()), time.Now().Unix(), req.Model, ChatCompletionDelta{
		Role:    "assistant",
		Content: "[FALLBACK] " + responseText,
	}, func() *string { s := "stop"; return &s }())

	c.JSON(http.StatusOK, response)
}

func chatHandlerStreamingFallback(c *gin.Context, req ChatRequest) {
	lastMsg := req.Messages[len(req.Messages)-1]
	thinkText, responseText := getResponseTexts(lastMsg.Content)
	delay := getModelDelay(req.Model)

	id := fmt.Sprintf("chatcmpl-%d", rand.Intn(999))
	timestamp := time.Now().Unix()

	// Send thinking process
	sendThinkingProcess(c, id, timestamp, req.Model, thinkText, delay)

	// Send response content with fallback indicator
	responseText = "[FALLBACK] " + responseText
	chunks := splitIntoChunks(responseText)
	for i, chunk := range chunks {
		select {
		case <-c.Request.Context().Done():
			return
		default:
			var finishReason *string
			if i == len(chunks)-1 {
				s := "stop"
				finishReason = &s
			}
			resp := createChatResponse(id, timestamp, req.Model,
				ChatCompletionDelta{Content: chunk}, finishReason)
			sendSSEResponse(c, resp)
			time.Sleep(delay)
		}
	}
}

func generateHandlerFallback(c *gin.Context, req GenerateRequest) {
	log.Printf("Using fallback mock response for generate request")
	
	// Determine language based on input content
	isChinese := containsEnglish(req.Prompt)

	var responseText string
	if isChinese {
		responseText = ChineseResponseText
	} else {
		responseText = EnglishResponseText
	}

	startTime := time.Now()
	loadDuration := 100 * time.Millisecond

	response := GenerateResponse{
		Model:           req.Model,
		CreatedAt:       time.Now().UTC(),
		Response:        "[FALLBACK] " + responseText,
		Done:            true,
		Context:         []int{1, 2, 3},
		TotalDuration:   time.Since(startTime),
		LoadDuration:    loadDuration,
		PromptEvalCount: 10,
		EvalCount:       20,
	}

	c.JSON(http.StatusOK, response)
}

// Helper functions
func transformOpenRouterMessages(messages []OpenRouterMessage) []Message {
	result := make([]Message, len(messages))
	for i, msg := range messages {
		result[i] = Message{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}
	return result
}

func sendSSEError(c *gin.Context, message string) {
	errorResp := map[string]interface{}{
		"error": map[string]string{
			"message": message,
			"type":    "api_error",
		},
	}
	sendSSEResponse(c, errorResp)
}
