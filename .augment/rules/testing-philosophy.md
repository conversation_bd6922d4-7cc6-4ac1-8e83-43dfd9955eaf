---
type: "always_apply"
---

# Testing Philosophy

## Core Philosophy
Create a comprehensive suite of executable specifications that validate all critical system behaviors, providing confidence in changes and serving as living documentation.

## The Four Pillars of Testing Excellence

### 1. The Golden Rule of Test Integrity
Application code changes to make tests pass. Tests never change to accommodate faulty application code.
- A failing test indicates a problem in the application logic, not the test specification
- When tests fail after code changes, first assume the application code is incorrect
- Only modify tests when requirements have genuinely changed

### 2. Tests as Executable Documentation
Every test must clearly communicate a specific behavioral requirement.
- Test names should read like specifications: `should_calculate_correct_discount_when_coupon_is_valid`
- Use the Given-When-Then pattern to structure test logic clearly
- Focus on testing behaviors and outcomes, not implementation details

### 3. Comprehensive Behavioral Coverage
Achieve 100% coverage of critical user journeys and business logic, not necessarily 100% code coverage.
- Every user story or business requirement must have corresponding test coverage
- Prioritize testing critical paths and edge cases over comprehensive code coverage
- Use risk-based testing to focus effort on high-impact, high-probability failure scenarios

### 4. Strategic Test Distribution
Apply the testing pyramid to optimize for both speed and confidence.

```
    /\     E2E Tests (10%)
   /  \    Slow, comprehensive, high confidence
  /____\   Test complete user workflows
 /      \  
/        \  Integration Tests (20%)
\        /  Medium speed, moderate scope
 \______/   Test component interactions
/        \  
\        /  Unit Tests (70%)
 \______/   Fast, focused, high volume
            Test individual components
```

## Test-Driven Development Workflow

### The Red-Green-Refactor Cycle

**Step 1: Define the Behavior (RED)**
- Write a clear behavioral specification in Given-When-Then format
- Implement this as a failing test that defines the expected outcome
- Confirm the test fails for the right reason

**Step 2: Implement Minimally (GREEN)**
- Write the simplest possible code to make the test pass
- Resist the urge to add features not covered by the current test
- Focus solely on satisfying the test requirements

**Step 3: Refactor Confidently (REFACTOR)**
- With tests passing, improve code structure, readability, and performance
- Refactor both application code and test code for maintainability
- Ensure tests continue to pass throughout refactoring process

## Testing Strategy by System Layer

### Unit Testing (The Foundation)
**Scope**: Individual functions, methods, or classes in complete isolation
**Speed**: Milliseconds per test
**Dependencies**: All external dependencies mocked or stubbed

### Integration Testing (The Contracts)
**Scope**: How multiple internal components work together
**Speed**: Seconds per test
**Dependencies**: Mock only external systems, use real internal dependencies

### End-to-End Testing (The User Journey)
**Scope**: Complete user workflows through the entire system
**Speed**: Minutes per test
**Dependencies**: Minimal mocking, use production-like environment

## Test Organization & Structure

### Test Structure Template
```
test_should_[expected_behavior]_when_[conditions]:
    // Given: Set up the initial state
    // When: Execute the behavior being tested  
    // Then: Verify the expected outcome
```

### Quality Gates
- All tests must pass before code can be committed
- New functionality requires corresponding test coverage
- No commented-out or skipped tests without documented justification
