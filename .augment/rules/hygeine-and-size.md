---
type: "always_apply"
---

Engineering Directive – Code Hygiene & Size Control
Maximum file length: 500 physical lines (including comments & blanks).
Before writing any new feature, evaluate whether it can be expressed within the limit; if not, split the file or extract cohesive sub-modules.
After every change, run an automatic refactor pass:
• Remove duplication (DRY)
• Inline single-use variables/functions
• Convert verbose imperative sequences into declarative helpers or utility calls
• Extract private helpers into separate files (*Helpers.ts, *Utils.ts) if they exceed 20 lines or are reused in ≥2 places
Prioritize clarity over cleverness: favor explicit naming, early returns, and guard clauses to shrink nesting depth.
Reject any commit that raises the file above 500 lines; open a refactor ticket instead.
Enforce via CI: add a pre-commit hook (git diff --numstat | awk '$1+$2 > 500 {exit 1}') and a GitHub Action that fails PRs violating the rule.