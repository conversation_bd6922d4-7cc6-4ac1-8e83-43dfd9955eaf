package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// PersistentConfig represents the saved user configuration
type PersistentConfig struct {
	Version           string                     `json:"version"`
	LastUpdated       time.Time                  `json:"last_updated"`
	Provider          string                     `json:"provider"`
	APIKey            string                     `json:"api_key"`
	ProviderSettings  map[string]interface{}     `json:"provider_settings,omitempty"`
	ModelAPIKeys      map[string]string          `json:"model_api_keys,omitempty"`
	ServerSettings    ServerSettings             `json:"server_settings,omitempty"`
}

// ServerSettings represents optional server configuration
type ServerSettings struct {
	Host               string `json:"host,omitempty"`
	Port               string `json:"port,omitempty"`
	LogLevel           string `json:"log_level,omitempty"`
	EnableRequestLog   *bool  `json:"enable_request_log,omitempty"`
	EnableResponseLog  *bool  `json:"enable_response_log,omitempty"`
	RequestTimeout     *int   `json:"request_timeout,omitempty"`
	MaxRetries         *int   `json:"max_retries,omitempty"`
}

const (
	ConfigVersion = "1.0"
	ConfigDirName = ".fake-ollama"
	ConfigFileName = "config.json"
)

// Get the configuration directory path
func getConfigDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	
	configDir := filepath.Join(homeDir, ConfigDirName)
	return configDir, nil
}

// Get the full configuration file path
func getConfigFilePath() (string, error) {
	configDir, err := getConfigDir()
	if err != nil {
		return "", err
	}
	
	return filepath.Join(configDir, ConfigFileName), nil
}

// Ensure configuration directory exists with proper permissions
func ensureConfigDir() error {
	configDir, err := getConfigDir()
	if err != nil {
		return err
	}
	
	// Create directory with restricted permissions (700 - owner only)
	if err := os.MkdirAll(configDir, 0700); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}
	
	return nil
}

// Load persistent configuration from file
func loadPersistentConfig() (*PersistentConfig, error) {
	configPath, err := getConfigFilePath()
	if err != nil {
		return nil, err
	}
	
	// Check if config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, nil // No config file exists yet
	}
	
	// Read config file
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}
	
	// Parse JSON
	var config PersistentConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}
	
	// Validate version compatibility
	if config.Version != ConfigVersion {
		return nil, fmt.Errorf("config file version %s is not compatible with current version %s", config.Version, ConfigVersion)
	}
	
	return &config, nil
}

// Save persistent configuration to file
func savePersistentConfig(config *PersistentConfig) error {
	if err := ensureConfigDir(); err != nil {
		return err
	}
	
	configPath, err := getConfigFilePath()
	if err != nil {
		return err
	}
	
	// Set version and timestamp
	config.Version = ConfigVersion
	config.LastUpdated = time.Now().UTC()
	
	// Marshal to JSON with indentation
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}
	
	// Write to file with restricted permissions (600 - owner read/write only)
	if err := os.WriteFile(configPath, data, 0600); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}
	
	return nil
}

// Delete persistent configuration file
func deletePersistentConfig() error {
	configPath, err := getConfigFilePath()
	if err != nil {
		return err
	}
	
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil // File doesn't exist, nothing to delete
	}
	
	if err := os.Remove(configPath); err != nil {
		return fmt.Errorf("failed to delete config file: %w", err)
	}
	
	return nil
}

// Check if persistent configuration exists
func hasPersistentConfig() bool {
	configPath, err := getConfigFilePath()
	if err != nil {
		return false
	}
	
	_, err = os.Stat(configPath)
	return err == nil
}

// Validate persistent configuration
func validatePersistentConfig(config *PersistentConfig) error {
	if config == nil {
		return fmt.Errorf("config is nil")
	}
	
	if config.Provider == "" {
		return fmt.Errorf("provider is required")
	}
	
	if config.APIKey == "" {
		return fmt.Errorf("API key is required")
	}
	
	// Validate provider
	validProviders := []string{"openrouter", "moonshot-direct", "anthropic", "openai"}
	isValidProvider := false
	for _, provider := range validProviders {
		if config.Provider == provider {
			isValidProvider = true
			break
		}
	}
	
	if !isValidProvider {
		return fmt.Errorf("invalid provider: %s", config.Provider)
	}
	
	return nil
}

// Create a new persistent configuration from CLI config
func createPersistentConfigFromCLI(cli *CLIConfig) *PersistentConfig {
	config := &PersistentConfig{
		Provider: cli.Provider,
		APIKey:   cli.getAPIKey(),
		ModelAPIKeys: make(map[string]string),
		ProviderSettings: make(map[string]interface{}),
	}
	
	// Add server settings if they differ from defaults
	if cli.Host != "0.0.0.0" || cli.Port != "11434" {
		config.ServerSettings.Host = cli.Host
		config.ServerSettings.Port = cli.Port
	}
	
	return config
}

// Apply persistent configuration to CLI config
func applyPersistentConfigToCLI(persistentConfig *PersistentConfig, cli *CLIConfig) {
	if persistentConfig == nil {
		return
	}
	
	// Only apply if CLI doesn't have explicit values
	if cli.Provider == "openrouter" && persistentConfig.Provider != "" {
		cli.Provider = persistentConfig.Provider
	}
	
	if cli.APIKey == "" && persistentConfig.APIKey != "" {
		cli.APIKey = persistentConfig.APIKey
	}
	
	// Apply server settings if not explicitly set
	if cli.Host == "0.0.0.0" && persistentConfig.ServerSettings.Host != "" {
		cli.Host = persistentConfig.ServerSettings.Host
	}
	
	if cli.Port == "11434" && persistentConfig.ServerSettings.Port != "" {
		cli.Port = persistentConfig.ServerSettings.Port
	}
}

// Get API key for a specific model (with fallback to provider default)
func getModelAPIKey(persistentConfig *PersistentConfig, modelName string) string {
	if persistentConfig == nil {
		return ""
	}
	
	// Check for model-specific API key first
	if modelKey, exists := persistentConfig.ModelAPIKeys[modelName]; exists && modelKey != "" {
		return modelKey
	}
	
	// Fall back to provider default API key
	return persistentConfig.APIKey
}

// Set API key for a specific model
func setModelAPIKey(persistentConfig *PersistentConfig, modelName, apiKey string) {
	if persistentConfig == nil {
		return
	}
	
	if persistentConfig.ModelAPIKeys == nil {
		persistentConfig.ModelAPIKeys = make(map[string]string)
	}
	
	persistentConfig.ModelAPIKeys[modelName] = apiKey
}

// Remove API key for a specific model
func removeModelAPIKey(persistentConfig *PersistentConfig, modelName string) {
	if persistentConfig == nil || persistentConfig.ModelAPIKeys == nil {
		return
	}
	
	delete(persistentConfig.ModelAPIKeys, modelName)
}

// Get configuration summary for display
func getConfigSummary(config *PersistentConfig) string {
	if config == nil {
		return "No saved configuration"
	}
	
	summary := fmt.Sprintf("Provider: %s", config.Provider)
	
	if config.APIKey != "" {
		summary += fmt.Sprintf(", API Key: %s...%s", 
			config.APIKey[:8], 
			config.APIKey[len(config.APIKey)-4:])
	}
	
	if len(config.ModelAPIKeys) > 0 {
		summary += fmt.Sprintf(", Model-specific keys: %d", len(config.ModelAPIKeys))
	}
	
	summary += fmt.Sprintf(", Last updated: %s", config.LastUpdated.Format("2006-01-02 15:04:05"))
	
	return summary
}
