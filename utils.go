package main

import (
	"time"

	"github.com/gin-gonic/gin"
)

// Constants for response text
const (
	// Chinese response text
	ChineseThinkText    = "Let me think about this."
	ChineseResponseText = "This is a fixed response from [fake-ollama](https://github.com/spoonnotfound/fake-ollama)."

	// English response text
	EnglishThinkText    = "Hmm, let me think about it."
	EnglishResponseText = "This is a fixed response from [fake-ollama](https://github.com/spoonnotfound/fake-ollama)."
)

// Modified helper function to split text into chunks by character
func splitIntoChunks(text string) []string {
	var chunks []string
	for _, r := range text {
		chunks = append(chunks, string(r))
	}
	return chunks
}

// Modified language detection function
func containsEnglish(str string) bool {
	for _, r := range str {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			return true
		}
	}
	return false
}

// Modified function to get response texts based on language
func getResponseTexts(input string) (thinkText, responseText string) {
	if !containsEnglish(input) { // If no English letters, return Chinese
		return ChineseThinkText, ChineseResponseText
	}
	return EnglishThinkText, EnglishResponseText
}

// Get model-specific delay for streaming
func getModelDelay(model string) time.Duration {
	switch model {
	case "deepseek-r1:671b":
		return 200 * time.Millisecond
	case "deepseek-r1:70b":
		return 100 * time.Millisecond
	case "deepseek-r1:32b":
		return 50 * time.Millisecond
	default:
		return 30 * time.Millisecond
	}
}

// SSE response helper functions
func sendSSEResponse(c *gin.Context, data interface{}) {
	c.SSEvent("", data)
	c.Writer.Flush()
}

func setupSSEHeaders(c *gin.Context) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
}
