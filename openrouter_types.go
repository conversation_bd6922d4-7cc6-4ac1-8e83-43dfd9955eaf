package main

import (
	"fmt"
	"time"
)

// OpenRouter API request and response types
// Based on OpenRouter API documentation: https://openrouter.ai/docs/api-reference

// OpenRouter chat completion request
type OpenRouterChatRequest struct {
	Model       string                `json:"model"`
	Messages    []OpenRouterMessage   `json:"messages"`
	Stream      *bool                 `json:"stream,omitempty"`
	MaxTokens   *int                  `json:"max_tokens,omitempty"`
	Temperature *float64              `json:"temperature,omitempty"`
	TopP        *float64              `json:"top_p,omitempty"`
	Stop        interface{}           `json:"stop,omitempty"` // string or []string
	User        string                `json:"user,omitempty"`
}

// OpenRouter message format
type OpenRouterMessage struct {
	Role    string `json:"role"`    // "system", "user", "assistant"
	Content string `json:"content"`
	Name    string `json:"name,omitempty"`
}

// OpenRouter chat completion response
type OpenRouterChatResponse struct {
	ID                string              `json:"id"`
	Object            string              `json:"object"`
	Created           int64               `json:"created"`
	Model             string              `json:"model"`
	Choices           []OpenRouterChoice  `json:"choices"`
	Usage             *OpenRouterUsage    `json:"usage,omitempty"`
	SystemFingerprint string              `json:"system_fingerprint,omitempty"`
}

// OpenRouter choice in response
type OpenRouterChoice struct {
	Index        int                `json:"index"`
	Message      OpenRouterMessage  `json:"message,omitempty"`      // For non-streaming
	Delta        OpenRouterMessage  `json:"delta,omitempty"`        // For streaming
	FinishReason string             `json:"finish_reason,omitempty"`
}

// OpenRouter usage statistics
type OpenRouterUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenRouter text completion request (for /api/generate compatibility)
type OpenRouterCompletionRequest struct {
	Model       string   `json:"model"`
	Prompt      string   `json:"prompt"`
	Stream      *bool    `json:"stream,omitempty"`
	MaxTokens   *int     `json:"max_tokens,omitempty"`
	Temperature *float64 `json:"temperature,omitempty"`
	TopP        *float64 `json:"top_p,omitempty"`
	Stop        interface{} `json:"stop,omitempty"`
	User        string   `json:"user,omitempty"`
}

// OpenRouter text completion response
type OpenRouterCompletionResponse struct {
	ID      string                      `json:"id"`
	Object  string                      `json:"object"`
	Created int64                       `json:"created"`
	Model   string                      `json:"model"`
	Choices []OpenRouterCompletionChoice `json:"choices"`
	Usage   *OpenRouterUsage            `json:"usage,omitempty"`
}

// OpenRouter completion choice
type OpenRouterCompletionChoice struct {
	Index        int    `json:"index"`
	Text         string `json:"text,omitempty"`         // For non-streaming
	Delta        string `json:"delta,omitempty"`        // For streaming
	FinishReason string `json:"finish_reason,omitempty"`
}

// OpenRouter error response
type OpenRouterError struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// OpenRouter models list response
type OpenRouterModelsResponse struct {
	Object string              `json:"object"`
	Data   []OpenRouterModel   `json:"data"`
}

// OpenRouter model information
type OpenRouterModel struct {
	ID           string                 `json:"id"`
	Object       string                 `json:"object"`
	Created      int64                  `json:"created"`
	OwnedBy      string                 `json:"owned_by"`
	Pricing      OpenRouterModelPricing `json:"pricing"`
	ContextLength int                   `json:"context_length"`
	Architecture OpenRouterArchitecture `json:"architecture"`
	TopProvider  OpenRouterProvider     `json:"top_provider"`
}

// OpenRouter model pricing
type OpenRouterModelPricing struct {
	Prompt     string `json:"prompt"`
	Completion string `json:"completion"`
	Request    string `json:"request,omitempty"`
	Image      string `json:"image,omitempty"`
}

// OpenRouter model architecture
type OpenRouterArchitecture struct {
	Modality    string `json:"modality"`
	Tokenizer   string `json:"tokenizer"`
	InstructType string `json:"instruct_type,omitempty"`
}

// OpenRouter provider information
type OpenRouterProvider struct {
	Order         int    `json:"order"`
	MaxCompletion int    `json:"max_completion_tokens,omitempty"`
}

// Request transformation functions

// Transform Ollama ChatRequest to OpenRouter format
func transformChatRequest(req ChatRequest, openRouterModel string) OpenRouterChatRequest {
	messages := make([]OpenRouterMessage, len(req.Messages))
	for i, msg := range req.Messages {
		messages[i] = OpenRouterMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	return OpenRouterChatRequest{
		Model:    openRouterModel,
		Messages: messages,
		Stream:   req.Stream,
	}
}

// Transform Ollama GenerateRequest to OpenRouter completion format
func transformGenerateRequest(req GenerateRequest, openRouterModel string) OpenRouterCompletionRequest {
	// Combine system and prompt if system is provided
	prompt := req.Prompt
	if req.System != "" {
		prompt = req.System + "\n\n" + req.Prompt
	}

	return OpenRouterCompletionRequest{
		Model:  openRouterModel,
		Prompt: prompt,
		Stream: req.Stream,
	}
}

// Transform OpenRouter chat response to Ollama format
func transformChatResponse(resp OpenRouterChatResponse) ChatResponse {
	choices := make([]ChatCompletionChoice, len(resp.Choices))
	for i, choice := range resp.Choices {
		var finishReason *string
		if choice.FinishReason != "" {
			finishReason = &choice.FinishReason
		}

		// Use delta for streaming, message for non-streaming
		var delta ChatCompletionDelta
		if choice.Delta.Content != "" || choice.Delta.Role != "" {
			delta = ChatCompletionDelta{
				Role:    choice.Delta.Role,
				Content: choice.Delta.Content,
			}
		} else {
			delta = ChatCompletionDelta{
				Role:    choice.Message.Role,
				Content: choice.Message.Content,
			}
		}

		choices[i] = ChatCompletionChoice{
			Index:        choice.Index,
			Delta:        delta,
			FinishReason: finishReason,
		}
	}

	return ChatResponse{
		ID:                resp.ID,
		Object:            resp.Object,
		Created:           resp.Created,
		Model:             resp.Model,
		SystemFingerprint: resp.SystemFingerprint,
		Choices:           choices,
	}
}

// Transform OpenRouter completion response to Ollama generate format
func transformGenerateResponse(resp OpenRouterCompletionResponse) GenerateResponse {
	var response string
	var done bool
	var finishReason string

	if len(resp.Choices) > 0 {
		choice := resp.Choices[0]
		if choice.Text != "" {
			response = choice.Text
		} else {
			response = choice.Delta
		}
		
		if choice.FinishReason != "" {
			finishReason = choice.FinishReason
			done = true
		}
	}

	return GenerateResponse{
		Model:      resp.Model,
		CreatedAt:  time.Unix(resp.Created, 0).UTC(),
		Response:   response,
		Done:       done,
		DoneReason: finishReason,
	}
}

// Model mapping helper function
func getOpenRouterModel(ollamaModel string, mappings []ModelMapping) (string, error) {
	for _, mapping := range mappings {
		if mapping.OllamaName == ollamaModel {
			return mapping.OpenRouterName, nil
		}
	}
	return "", fmt.Errorf("model not found: %s", ollamaModel)
}
