# OpenRouter Proxy Configuration
# Copy this file to .env and set your actual values

# Required: Your OpenRouter API key
# Get one at: https://openrouter.ai/keys
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here

# Optional: OpenRouter API base URL (default: https://openrouter.ai/api/v1)
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Optional: Model mapping configuration file (default: models.json)
MODEL_MAPPING_FILE=models.json

# Optional: Server configuration
OLLAMA_HOST=0.0.0.0:11434

# Optional: Logging configuration
LOG_LEVEL=info                    # debug, info, warn, error
ENABLE_REQUEST_LOG=true           # Log incoming requests
ENABLE_RESPONSE_LOG=true          # Log outgoing responses

# Optional: Request configuration
REQUEST_TIMEOUT=30                # Request timeout in seconds
MAX_RETRIES=3                     # Maximum retry attempts

# Optional: Performance tuning
MAX_CONCURRENT_REQUESTS=100       # Maximum concurrent requests
OPENROUTER_TIMEOUT=25             # OpenRouter API timeout in seconds

# Optional: Security and rate limiting
CORS_ORIGINS=*                    # CORS allowed origins
RATE_LIMIT_REQUESTS=1000          # Requests per minute per IP
RATE_LIMIT_WINDOW=1m              # Rate limit window

# Optional: Monitoring
METRICS_ENABLED=true              # Enable Prometheus metrics
METRICS_PORT=9090                 # Metrics server port
HEALTH_CHECK_PATH=/health         # Health check endpoint path
