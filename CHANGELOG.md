# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **OpenRouter Integration**: Complete proxy functionality for accessing real AI models
- **Configuration Management**: Environment-based configuration with validation
- **Model Mapping System**: Configurable mapping between Ollama and OpenRouter models
- **Request/Response Logging**: Comprehensive monitoring and debugging capabilities
- **Code Hygiene Enforcement**: 500-line maximum file length with automated validation
- **Pre-commit Hooks**: Automatic file size validation before commits
- **GitHub Actions**: Enhanced CI pipeline with code hygiene checks
- **Comprehensive Documentation**: Complete guides for deployment, configuration, and development

### Changed
- **Architecture Refactoring**: Split monolithic main.go (1,154 lines) into 8 focused modules
- **Language Standardization**: Replaced all Chinese comments and text with English
- **API Compatibility**: Enhanced Ollama API compatibility with proper error handling
- **Error Handling**: Replaced fallback mock responses with clear configuration errors
- **Type Safety**: Updated `interface{}` to `any` for Go 1.18+ compatibility

### Improved
- **Code Quality**: Applied Go standards with gofmt and goimports
- **Documentation**: Complete rewrite of README with usage examples and deployment guides
- **Testing**: Enhanced test infrastructure with race detection
- **Security**: Secure credential management with environment variables
- **Performance**: Optimized request/response handling and streaming

### Fixed
- **Dead Code Removal**: Removed unused functions and imports
- **Memory Leaks**: Fixed potential connection leaks in streaming responses
- **Error Messages**: Improved error messages with actionable guidance

## [0.5.7] - Previous Version

### Features
- Basic Ollama API simulation
- Mock responses for DeepSeek-R1 models
- Streaming support
- OpenAI compatibility layer
- Docker support

### Limitations
- Fixed mock responses only
- Chinese-language interface
- Monolithic codebase structure
- Limited configuration options

---

## Migration Guide

### From v0.5.7 to Unreleased

**Breaking Changes:**
- **Required Configuration**: `OPENROUTER_API_KEY` environment variable is now required
- **Language Change**: All interfaces now use English instead of Chinese
- **Error Behavior**: No longer falls back to mock responses; returns configuration errors

**Migration Steps:**

1. **Set OpenRouter API Key**:
   ```bash
   export OPENROUTER_API_KEY="your-api-key-here"
   ```

2. **Update Docker Commands**:
   ```bash
   # Old
   docker run -d -p 11434:11434 spoonnotfound/fake-ollama
   
   # New
   docker run -d -p 11434:11434 \
     -e OPENROUTER_API_KEY="your-key" \
     spoonnotfound/fake-ollama
   ```

3. **Configuration Files** (Optional):
   - Create `models.json` for custom model mappings
   - Use `.env` file for environment variables

4. **Logging Configuration** (Optional):
   ```bash
   export LOG_LEVEL=info
   export ENABLE_REQUEST_LOG=true
   export ENABLE_RESPONSE_LOG=true
   ```

**Benefits After Migration:**
- ✅ Real AI model responses instead of mock data
- ✅ Access to GPT-4, Claude, Gemini, and more
- ✅ Configurable model mappings
- ✅ Comprehensive logging and monitoring
- ✅ Production-ready error handling
- ✅ English-language interface

**Compatibility:**
- ✅ All existing Ollama clients work without code changes
- ✅ Same API endpoints and response formats
- ✅ Streaming responses work identically
- ✅ OpenAI compatibility maintained

---

## Development Notes

### Code Architecture Evolution

**Before (v0.5.7):**
- Single file: `main.go` (1,154 lines)
- Mixed Chinese/English comments
- Mock responses only
- Limited configuration

**After (Unreleased):**
- 8 focused modules (avg 131 lines each)
- English-only codebase
- Real AI model integration
- Comprehensive configuration system

### Quality Improvements

**Code Hygiene:**
- Enforced 500-line maximum per file
- Pre-commit hooks for validation
- GitHub Actions quality gates
- Go standard formatting

**Documentation:**
- Complete README rewrite
- Comprehensive deployment guide
- API compatibility documentation
- Development setup instructions

**Testing:**
- Enhanced test infrastructure
- Race condition detection
- Integration test framework
- Performance benchmarking

### Future Roadmap

**Planned Features:**
- Advanced model routing based on query complexity
- A/B testing framework for model comparisons
- User preference management
- Load balancing across multiple providers
- Cost optimization algorithms
- Multi-modal model support (images, audio)

**Technical Improvements:**
- Package structure reorganization
- Interface extraction for better testability
- Dependency injection framework
- Enhanced configuration validation
- Metrics and observability improvements
