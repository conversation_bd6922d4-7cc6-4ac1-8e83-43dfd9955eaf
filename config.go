package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
)

// Configuration structures for OpenRouter integration
type Config struct {
	OpenRouterAPIKey  string `json:"openrouter_api_key"`
	OpenRouterBaseURL string `json:"openrouter_base_url"`
	ModelMappingFile  string `json:"model_mapping_file"`
	LogLevel          string `json:"log_level"`
	EnableRequestLog  bool   `json:"enable_request_log"`
	EnableResponseLog bool   `json:"enable_response_log"`
	RequestTimeout    int    `json:"request_timeout"`
	MaxRetries        int    `json:"max_retries"`
}

type ModelMapping struct {
	OllamaName      string            `json:"ollama_name"`
	OpenRouterName  string            `json:"openrouter_name"`
	Provider        string            `json:"provider"`
	ContextLength   int               `json:"context_length"`
	CostPer1KTokens map[string]string `json:"cost_per_1k_tokens"`
	Capabilities    []string          `json:"capabilities"`
	Description     string            `json:"description"`
	FallbackModels  []string          `json:"fallback_models"`
}

type ModelMappingConfig struct {
	Version         string         `json:"version"`
	DefaultProvider string         `json:"default_provider"`
	FallbackModel   string         `json:"fallback_model"`
	Mappings        []ModelMapping `json:"mappings"`
}

// Configuration loading functions
func loadConfig() *Config {
	config := &Config{
		OpenRouterAPIKey:  os.Getenv("OPENROUTER_API_KEY"),
		OpenRouterBaseURL: getEnvOrDefault("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
		ModelMappingFile:  getEnvOrDefault("MODEL_MAPPING_FILE", "models.json"),
		LogLevel:          getEnvOrDefault("LOG_LEVEL", "info"),
		EnableRequestLog:  getEnvBool("ENABLE_REQUEST_LOG", true),
		EnableResponseLog: getEnvBool("ENABLE_RESPONSE_LOG", true),
		RequestTimeout:    getEnvInt("REQUEST_TIMEOUT", 30),
		MaxRetries:        getEnvInt("MAX_RETRIES", 3),
	}

	return config
}

// Validate config for production use
func validateConfig(config *Config) {
	if config.OpenRouterAPIKey == "" {
		log.Fatal("OPENROUTER_API_KEY environment variable is required. Please set your OpenRouter API key.")
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func loadModelMappings(configFile string) (map[string]ModelMapping, error) {
	mappings := make(map[string]ModelMapping)

	// Check if config file exists
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		log.Printf("Model mapping file %s not found, using default mappings", configFile)
		return getDefaultModelMappings(), nil
	}

	// Read and parse config file
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read model mapping file: %w", err)
	}

	var config ModelMappingConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse model mapping file: %w", err)
	}

	// Convert to map for quick lookup
	for _, mapping := range config.Mappings {
		mappings[mapping.OllamaName] = mapping
	}

	log.Printf("Loaded %d model mappings from %s", len(mappings), configFile)
	return mappings, nil
}

func getDefaultModelMappings() map[string]ModelMapping {
	return map[string]ModelMapping{
		"deepseek-r1:14b": {
			OllamaName:     "deepseek-r1:14b",
			OpenRouterName: "deepseek/deepseek-r1",
			Provider:       "deepseek",
			ContextLength:  32768,
			CostPer1KTokens: map[string]string{
				"input":  "0.0014",
				"output": "0.0028",
			},
			Capabilities: []string{"text", "reasoning"},
			Description:  "DeepSeek R1 model for general purpose tasks",
		},
		"deepseek-r1:32b": {
			OllamaName:     "deepseek-r1:32b",
			OpenRouterName: "openai/gpt-4o",
			Provider:       "openai",
			ContextLength:  128000,
			CostPer1KTokens: map[string]string{
				"input":  "0.005",
				"output": "0.015",
			},
			Capabilities: []string{"text", "reasoning", "function_calling"},
			Description:  "GPT-4o for high-quality responses",
		},
		"deepseek-r1:70b": {
			OllamaName:     "deepseek-r1:70b",
			OpenRouterName: "anthropic/claude-3.5-sonnet",
			Provider:       "anthropic",
			ContextLength:  200000,
			CostPer1KTokens: map[string]string{
				"input":  "0.003",
				"output": "0.015",
			},
			Capabilities: []string{"text", "reasoning", "analysis"},
			Description:  "Claude 3.5 Sonnet for advanced reasoning",
		},
		"deepseek-r1:671b": {
			OllamaName:     "deepseek-r1:671b",
			OpenRouterName: "openai/o1-preview",
			Provider:       "openai",
			ContextLength:  128000,
			CostPer1KTokens: map[string]string{
				"input":  "0.015",
				"output": "0.060",
			},
			Capabilities: []string{"text", "reasoning", "research"},
			Description:  "OpenAI O1 Preview for complex reasoning tasks",
		},
	}
}
