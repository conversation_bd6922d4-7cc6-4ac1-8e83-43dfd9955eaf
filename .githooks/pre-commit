#!/bin/bash

# Pre-commit hook to enforce 500-line maximum file length
# Engineering Directive – Code Hygiene & Size Control

set -e

echo "🔍 Checking file sizes (500-line limit)..."

# Check all Go files for line count
violations=()
while IFS= read -r -d '' file; do
    if [[ "$file" == *.go ]]; then
        lines=$(wc -l < "$file")
        if [ "$lines" -gt 500 ]; then
            violations+=("$file: $lines lines (exceeds 500-line limit)")
        fi
    fi
done < <(git diff --cached --name-only -z)

# Report violations
if [ ${#violations[@]} -gt 0 ]; then
    echo "❌ Code hygiene violation: Files exceed 500-line limit"
    echo ""
    for violation in "${violations[@]}"; do
        echo "  • $violation"
    done
    echo ""
    echo "Please refactor these files into smaller, cohesive modules:"
    echo "  • Extract private helpers into separate files (*Helpers.go, *Utils.go)"
    echo "  • Split large files by functional responsibility"
    echo "  • Remove duplication (DRY principle)"
    echo "  • Inline single-use variables/functions"
    echo ""
    echo "Commit rejected. Fix violations and try again."
    exit 1
fi

# Check for any files that might have grown close to the limit
warnings=()
while IFS= read -r -d '' file; do
    if [[ "$file" == *.go ]]; then
        lines=$(wc -l < "$file")
        if [ "$lines" -gt 400 ] && [ "$lines" -le 500 ]; then
            warnings+=("$file: $lines lines (approaching 500-line limit)")
        fi
    fi
done < <(git diff --cached --name-only -z)

# Report warnings
if [ ${#warnings[@]} -gt 0 ]; then
    echo "⚠️  Files approaching 500-line limit:"
    for warning in "${warnings[@]}"; do
        echo "  • $warning"
    done
    echo ""
fi

echo "✅ All files within 500-line limit"
exit 0
