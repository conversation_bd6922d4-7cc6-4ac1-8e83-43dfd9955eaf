# fake-ollama: OpenRouter Proxy

A production-ready OpenRouter proxy that maintains complete Ollama API compatibility while delivering authentic LLM responses from real AI models.

## Overview

Transform your existing Ollama clients to access premium AI models (GPT-4, <PERSON>, <PERSON>, etc.) through OpenRouter without any code changes. This proxy provides a transparent bridge between the familiar Ollama API and OpenRouter's extensive model catalog.

**Architecture Flow:**
```
Ollama Client → fake-ollama Proxy → OpenRouter API → Real LLM → Authentic Response → Client
```

## Features

- **🔄 100% Ollama API Compatibility** - Drop-in replacement for existing Ollama clients
- **🤖 Real AI Models** - Access GPT-4, <PERSON>, <PERSON>, and more via OpenRouter
- **⚡ Streaming Support** - Real-time streaming responses with proper connection handling
- **🎯 Smart Model Mapping** - Configurable mapping between Ollama and OpenRouter model names
- **📊 Request/Response Logging** - Comprehensive monitoring and debugging capabilities
- **🛡️ Production Ready** - Robust error handling, configuration validation, and security
- **🔧 Easy Configuration** - Environment-based configuration with sensible defaults
- **📖 OpenAI Compatible** - Supports both Ollama and OpenAI API formats

## Quick Start

### Prerequisites

1. **OpenRouter API Key**: Get one at [openrouter.ai/keys](https://openrouter.ai/keys)
2. **Go 1.21+** (for building from source)

### Using Docker

```bash
# Pull the latest image
docker pull ghcr.io/spoonnotfound/fake-ollama:latest

# Run with your OpenRouter API key
docker run -d \
  --name fake-ollama \
  -p 11434:11434 \
  -e OPENROUTER_API_KEY="your-api-key-here" \
  ghcr.io/spoonnotfound/fake-ollama:latest
```

### From Source

```bash
# Clone the repository
git clone https://github.com/spoonnotfound/fake-ollama.git
cd fake-ollama

# Set your OpenRouter API key
export OPENROUTER_API_KEY="your-api-key-here"

# Build and run
go build -o fake-ollama
./fake-ollama
```

### Docker Compose

```yaml
version: '3.8'
services:
  fake-ollama:
    image: ghcr.io/spoonnotfound/fake-ollama:latest
    ports:
      - "11434:11434"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - LOG_LEVEL=info
    restart: unless-stopped
```

## Command Line Interface

fake-ollama includes a comprehensive CLI for easy configuration and management:

### Interactive Setup (Recommended for First-Time Users)

```bash
# Just run without parameters - interactive mode guides you through setup
./fake-ollama

# The interactive mode will:
# 1. Prompt you to select an AI provider
# 2. Guide you through API key entry and validation
# 3. Configure optional settings (port, host, logging)
# 4. Save configuration for future use
# 5. Start the server automatically
```

### Quick CLI Examples

```bash
# Show all available options
./fake-ollama --help

# List available models (including Kimi K2)
./fake-ollama --list-models

# Store API key securely
./fake-ollama --set-api-key sk-or-v1-your-openrouter-key

# Test configuration without starting server
./fake-ollama --dry-run --verbose

# Start with Kimi K2 via OpenRouter
./fake-ollama --provider openrouter --api-key your-key

# Use Moonshot AI directly for Kimi K2
./fake-ollama --provider moonshot-direct --api-key your-moonshot-key

# Custom server configuration
./fake-ollama --host 127.0.0.1 --port 8080 --verbose
```

### Kimi K2 Integration with VS Code

```bash
# 1. Start fake-ollama with Kimi K2 support
./fake-ollama --provider openrouter --api-key sk-or-v1-your-key

# 2. Configure VS Code native Ollama integration:
#    - Set Ollama endpoint: http://localhost:11434
#    - Select model: kimi-k2:latest or kimi-k2:coding

# 3. Test the integration
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "kimi-k2:coding",
    "messages": [
      {"role": "user", "content": "Write a Python function to implement binary search"}
    ]
  }'
```

For complete CLI documentation, see [CLI Reference](docs/cli-reference.md).

## Configuration

### Required Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `OPENROUTER_API_KEY` | Your OpenRouter API key | `sk-or-v1-...` |

### Optional Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `OPENROUTER_BASE_URL` | `https://openrouter.ai/api/v1` | OpenRouter API endpoint |
| `MODEL_MAPPING_FILE` | `models.json` | Custom model mapping file |
| `OLLAMA_HOST` | `0.0.0.0:11434` | Server bind address |
| `LOG_LEVEL` | `info` | Logging level (debug, info, warn, error) |
| `ENABLE_REQUEST_LOG` | `true` | Enable request logging |
| `ENABLE_RESPONSE_LOG` | `true` | Enable response logging |
| `REQUEST_TIMEOUT` | `30` | Request timeout in seconds |

### Model Mapping

The proxy maps Ollama model names to OpenRouter models. Default mappings:

| Ollama Model | OpenRouter Model | Provider | Use Case |
|--------------|------------------|----------|----------|
| `deepseek-r1:14b` | `deepseek/deepseek-r1` | DeepSeek | General purpose |
| `deepseek-r1:32b` | `openai/gpt-4o` | OpenAI | High quality |
| `deepseek-r1:70b` | `anthropic/claude-3.5-sonnet` | Anthropic | Advanced reasoning |
| `deepseek-r1:671b` | `openai/o1-preview` | OpenAI | Complex reasoning |

Customize mappings by creating a `models.json` file (see [model-mapping.md](docs/model-mapping.md) for details).

## Usage Examples

### With Ollama Python Client

```python
import ollama

# No code changes needed - just point to fake-ollama
client = ollama.Client(host='http://localhost:11434')

# Use familiar Ollama model names
response = client.chat(
    model='deepseek-r1:32b',  # Maps to GPT-4o via OpenRouter
    messages=[
        {'role': 'user', 'content': 'Explain quantum computing'}
    ]
)

print(response['message']['content'])
```

### With curl

```bash
# Chat completion
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:70b",
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci"}
    ]
  }'

# Streaming response
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:14b",
    "messages": [{"role": "user", "content": "Tell me a story"}],
    "stream": true
  }'
```

### OpenAI Compatible

```python
from openai import OpenAI

# Use OpenAI SDK with fake-ollama
client = OpenAI(
    base_url="http://localhost:11434/v1",
    api_key="not-needed"  # API key handled by proxy
)

response = client.chat.completions.create(
    model="deepseek-r1:671b",  # Maps to O1-Preview
    messages=[{"role": "user", "content": "Solve this math problem..."}]
)
```

## API Compatibility

### Supported Ollama Endpoints

- ✅ `POST /api/chat` - Chat completions with streaming
- ✅ `POST /api/generate` - Text generation with streaming
- ✅ `GET /api/tags` - List available models
- ✅ `POST /api/pull` - Model pull simulation
- ✅ `POST /api/embed` - Text embeddings
- ✅ `POST /api/create` - Model creation simulation
- ✅ `POST /api/show` - Model information
- ✅ `POST /api/copy` - Model copying simulation
- ✅ `DELETE /api/delete` - Model deletion simulation

### OpenAI Compatible Endpoints

- ✅ `POST /v1/chat/completions` - Chat completions
- ✅ `POST /v1/completions` - Text completions
- ✅ `POST /v1/embeddings` - Text embeddings
- ✅ `GET /v1/models` - List models
- ✅ `GET /v1/models/{model}` - Model details

## Architecture

The codebase follows strict engineering principles with a 500-line maximum per file:

```
fake-ollama/
├── main.go              (103 lines) - Application entry point
├── config.go            (166 lines) - Configuration management
├── models.go            (137 lines) - Model definitions and utilities
├── types.go             (143 lines) - Request/response type definitions
├── utils.go             (73 lines)  - Utility functions and helpers
├── handlers.go          (250 lines) - Main API handlers
├── handlers_misc.go     (170 lines) - Secondary API handlers
├── middleware.go        (128 lines) - Gin middleware functions
└── docs/                          - Comprehensive documentation
```

**Quality Assurance:**
- Pre-commit hooks enforce file size limits
- GitHub Actions validate code hygiene
- Comprehensive test coverage
- Production-ready error handling

## Monitoring and Logging

### Request/Response Logging

```bash
# Enable debug logging
export LOG_LEVEL=debug
export ENABLE_REQUEST_LOG=true
export ENABLE_RESPONSE_LOG=true

./fake-ollama
```

### Log Output Examples

```
2025-01-22T10:30:45Z [REQUEST] IP: *************, Model: deepseek-r1:32b, UserAgent: ollama-python/0.1.0, Path: /api/chat
2025-01-22T10:30:47Z [RESPONSE] Model: deepseek-r1:32b, Status: 200, Duration: 1.2s, Path: /api/chat
```

## Deployment

### Production Deployment

See [deployment-guide.md](docs/deployment-guide.md) for comprehensive deployment options including:

- Docker and Docker Compose
- Kubernetes with Helm charts
- Load balancing and high availability
- Monitoring and alerting setup
- Security best practices

### Health Checks

```bash
# Health check endpoint
curl http://localhost:11434/

# Version information
curl http://localhost:11434/api/version
```

## Development

### Building from Source

```bash
# Clone and build
git clone https://github.com/spoonnotfound/fake-ollama.git
cd fake-ollama
go mod tidy
go build -o fake-ollama

# Run tests
go test -v ./...

# Install pre-commit hooks
git config core.hooksPath .githooks
```

### Code Quality

This project enforces strict code hygiene:

- **500-line maximum** per file
- **Pre-commit hooks** validate file sizes
- **GitHub Actions** enforce quality gates
- **Go standards** with gofmt and goimports
- **Comprehensive testing** with race detection

## Troubleshooting

### Common Issues

**Missing API Key**
```
OPENROUTER_API_KEY environment variable is required. Please set your OpenRouter API key.
```
**Solution:** Set your OpenRouter API key: `export OPENROUTER_API_KEY="sk-or-v1-..."`

**Model Not Found**
```
model "unknown-model" not found, try pulling it first
```
**Solution:** Use supported model names or customize model mappings in `models.json`

**Connection Issues**
```bash
# Test OpenRouter connectivity
curl -H "Authorization: Bearer $OPENROUTER_API_KEY" \
  https://openrouter.ai/api/v1/models
```

### Debug Mode

```bash
# Enable detailed logging
export LOG_LEVEL=debug
./fake-ollama
```

## Contributing

We welcome contributions! Please see our [contributing guidelines](CONTRIBUTING.md).

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes (respecting the 500-line limit)
4. Run tests: `go test -v ./...`
5. Submit a pull request

### Code Standards

- Follow Go conventions and use `gofmt`
- Maintain the 500-line file limit
- Add tests for new functionality
- Update documentation as needed

## License

MIT License - see [LICENSE](LICENSE) for details.

## Acknowledgments

- [Ollama](https://ollama.ai/) for the excellent API design
- [OpenRouter](https://openrouter.ai/) for providing access to multiple AI models
- The Go community for excellent tooling and libraries

---

**Need Help?**
- 📖 Check the [documentation](docs/)
- 🐛 Report issues on [GitHub](https://github.com/spoonnotfound/fake-ollama/issues)
- 💬 Join discussions in [GitHub Discussions](https://github.com/spoonnotfound/fake-ollama/discussions)