package main

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"
	"syscall"
	"time"

	"golang.org/x/term"
)

// Interactive CLI configuration
type InteractiveConfig struct {
	MaxRetries      int
	TimeoutSeconds  int
	SaveConfig      bool
}

// ModelInfo represents basic model information
type ModelInfo struct {
	Name        string
	Provider    string
	Description string
	Available   bool
}

// Check for existing configuration and offer to use it
func checkExistingConfiguration(cli *CLIConfig, config *InteractiveConfig) error {
	// Check if persistent configuration exists
	if !hasPersistentConfig() {
		return nil // No existing config, proceed with normal setup
	}

	// Load existing configuration
	persistentConfig, err := loadPersistentConfig()
	if err != nil {
		fmt.Printf("⚠️  Found saved configuration but failed to load it: %v\n", err)
		if askYesNo("Would you like to continue with fresh setup?") {
			return nil
		}
		return fmt.Errorf("configuration load cancelled")
	}

	// Display existing configuration
	fmt.Printf("📁 Found existing configuration:\n")
	fmt.Printf("   %s\n\n", getConfigSummary(persistentConfig))

	// Ask user what to do
	fmt.Printf("What would you like to do?\n")
	fmt.Printf("1. Use existing configuration\n")
	fmt.Printf("2. Reconfigure with new settings\n")
	fmt.Printf("3. Exit\n\n")

	for attempt := 1; attempt <= config.MaxRetries; attempt++ {
		fmt.Printf("Enter your choice (1-3): ")

		var choice string
		if _, err := fmt.Scanln(&choice); err != nil {
			fmt.Printf("❌ Invalid input. Please try again.\n")
			continue
		}

		switch choice {
		case "1":
			// Use existing configuration
			applyPersistentConfigToCLI(persistentConfig, cli)
			fmt.Printf("✅ Using existing configuration\n")
			fmt.Printf("🚀 Starting server with saved settings...\n\n")

			// Skip interactive setup by setting required values
			if cli.getAPIKey() != "" {
				// Configuration is complete, we can exit interactive mode
				return fmt.Errorf("SKIP_INTERACTIVE") // Special error to skip interactive setup
			}
			return nil

		case "2":
			// Continue with reconfiguration
			fmt.Printf("🔄 Proceeding with new configuration setup...\n\n")
			return nil

		case "3":
			// Exit
			return fmt.Errorf("setup cancelled by user")

		default:
			fmt.Printf("❌ Invalid choice. Please enter 1, 2, or 3.\n")
			if attempt < config.MaxRetries {
				continue
			}
		}
	}

	return fmt.Errorf("failed to get valid choice after %d attempts", config.MaxRetries)
}

// Interactive prompts for missing configuration
func handleInteractiveMode(cli *CLIConfig) error {
	if cli.NonInteractive {
		return fmt.Errorf("required parameters missing and non-interactive mode enabled")
	}

	fmt.Printf("\n🎯 Welcome to fake-ollama Interactive Setup\n")
	fmt.Printf("═══════════════════════════════════════════\n\n")
	fmt.Printf("fake-ollama is a proxy service - all models require API keys from their providers.\n")
	fmt.Printf("No models are installed locally. Let's configure your AI proxy step by step.\n")
	fmt.Printf("You can exit anytime with Ctrl+C.\n\n")

	interactive := &InteractiveConfig{
		MaxRetries:     3,
		TimeoutSeconds: 10,
		SaveConfig:     true,
	}

	// Check for existing configuration
	if err := checkExistingConfiguration(cli, interactive); err != nil {
		return err
	}

	// Step 1: Provider selection
	if cli.Provider == "openrouter" && cli.getAPIKey() == "" {
		provider, err := promptProviderSelection(interactive)
		if err != nil {
			return err
		}
		cli.Provider = provider
	}

	// Step 2: API key
	if cli.getAPIKey() == "" {
		apiKey, err := promptAPIKey(cli.Provider, interactive)
		if err != nil {
			return err
		}
		cli.APIKey = apiKey
	}

	// Step 3: Model selection and validation
	if err := promptModelSelection(cli, interactive); err != nil {
		return err
	}

	// Step 4: Optional configuration
	if err := promptOptionalConfig(cli, interactive); err != nil {
		return err
	}

	// Step 5: Validate complete configuration
	fmt.Printf("\n🔍 Validating configuration...\n")
	if err := validateInteractiveConfig(cli, interactive); err != nil {
		return err
	}

	// Step 5: Save configuration
	if interactive.SaveConfig {
		if err := saveInteractiveConfig(cli); err != nil {
			fmt.Printf("⚠️  Warning: Could not save configuration: %v\n", err)
		} else {
			fmt.Printf("✅ Configuration saved successfully\n")
		}
	}

	fmt.Printf("\n🚀 Configuration complete! Starting server...\n\n")
	return nil
}

// Prompt for provider selection with numbered menu
func promptProviderSelection(config *InteractiveConfig) (string, error) {
	providers := []struct {
		key  string
		name string
		desc string
	}{
		{"openrouter", "OpenRouter", "Access to multiple AI models (recommended)"},
		{"moonshot-direct", "Moonshot AI", "Direct access to Kimi K2 models"},
		{"anthropic", "Anthropic", "Direct access to Claude models"},
		{"openai", "OpenAI", "Direct access to GPT models"},
	}

	for attempt := 1; attempt <= config.MaxRetries; attempt++ {
		fmt.Printf("📡 Select AI Provider:\n")
		for i, provider := range providers {
			fmt.Printf("  %d) %s - %s\n", i+1, provider.name, provider.desc)
		}
		fmt.Printf("\nEnter your choice (1-%d): ", len(providers))

		reader := bufio.NewReader(os.Stdin)
		input, err := reader.ReadString('\n')
		if err != nil {
			return "", fmt.Errorf("failed to read input: %v", err)
		}

		input = strings.TrimSpace(input)
		choice, err := strconv.Atoi(input)
		if err != nil || choice < 1 || choice > len(providers) {
			fmt.Printf("❌ Invalid choice. Please enter a number between 1 and %d.\n\n", len(providers))
			if !askRetry(attempt, config.MaxRetries) {
				return "", fmt.Errorf("provider selection cancelled")
			}
			continue
		}

		selected := providers[choice-1]
		fmt.Printf("✅ Selected: %s (%s)\n\n", selected.name, selected.key)
		return selected.key, nil
	}

	return "", fmt.Errorf("failed to select provider after %d attempts", config.MaxRetries)
}

// Prompt for API key with masked input
func promptAPIKey(provider string, config *InteractiveConfig) (string, error) {
	envVar := getAPIKeyEnvVar(provider)
	
	for attempt := 1; attempt <= config.MaxRetries; attempt++ {
		fmt.Printf("🔑 Enter API Key for %s:\n", provider)
		fmt.Printf("   Environment variable: %s\n", envVar)
		fmt.Printf("   Expected format: %s\n", getAPIKeyFormat(provider))
		fmt.Printf("   💡 Tip: You can paste your API key (Ctrl+V or Cmd+V)\n")
		fmt.Printf("\n")

		// Read password with masking
		apiKey, err := readMaskedInput()
		if err != nil {
			fmt.Printf("❌ Masked input failed: %v\n", err)
			fmt.Printf("💡 Would you like to try entering the API key in plain text? (y/n): ")

			if askYesNo("") {
				fmt.Printf("Enter API key (will be visible): ")
				reader := bufio.NewReader(os.Stdin)
				input, err := reader.ReadString('\n')
				if err != nil {
					return "", fmt.Errorf("failed to read API key: %v", err)
				}
				apiKey = strings.TrimSpace(input)
			} else {
				return "", fmt.Errorf("API key entry cancelled")
			}
		}

		if len(apiKey) == 0 {
			fmt.Printf("❌ API key cannot be empty.\n\n")
			if !askRetry(attempt, config.MaxRetries) {
				return "", fmt.Errorf("API key entry cancelled")
			}
			continue
		}

		// Validate format
		if err := validateAPIKeyFormat(provider, apiKey); err != nil {
			fmt.Printf("❌ Invalid API key format: %v\n", err)
			fmt.Printf("💡 Make sure you copied the complete API key without extra spaces\n\n")
			if !askRetry(attempt, config.MaxRetries) {
				return "", fmt.Errorf("API key validation cancelled")
			}
			continue
		}

		// Test API connectivity
		fmt.Printf("\n🔍 Testing API key connectivity...")
		if err := testAPIKeyInteractive(provider, apiKey, config); err != nil {
			fmt.Printf("\n❌ API key test failed: %v\n", err)
			fmt.Printf("💡 Troubleshooting:\n")
			fmt.Printf("   • Check your API key is valid for %s\n", provider)
			fmt.Printf("   • Ensure you have sufficient credits/quota\n")
			fmt.Printf("   • Verify network connectivity\n\n")
			
			if !askRetry(attempt, config.MaxRetries) {
				return "", fmt.Errorf("API key testing cancelled")
			}
			continue
		}

		fmt.Printf("\n✅ API key validated successfully!\n\n")
		return apiKey, nil
	}

	return "", fmt.Errorf("failed to validate API key after %d attempts", config.MaxRetries)
}

// Prompt for model selection and validation
func promptModelSelection(cli *CLIConfig, config *InteractiveConfig) error {
	fmt.Printf("📋 Model Selection and Validation:\n")
	fmt.Printf("   All models require valid API keys - no local models available\n\n")

	// Apply current configuration to load models
	if err := cli.apply(); err != nil {
		return fmt.Errorf("failed to apply configuration: %v", err)
	}

	// Load available models for the provider
	models, err := getAvailableModelsForProvider(cli.Provider, cli.getAPIKey())
	if err != nil {
		fmt.Printf("❌ Failed to load models for %s: %v\n", cli.Provider, err)
		fmt.Printf("💡 This might be due to:\n")
		fmt.Printf("   • Invalid API key\n")
		fmt.Printf("   • Network connectivity issues\n")
		fmt.Printf("   • Provider service unavailable\n\n")

		if !askYesNo("Would you like to continue anyway? (models will be loaded at runtime)") {
			return fmt.Errorf("model validation cancelled")
		}
		return nil
	}

	if len(models) == 0 {
		fmt.Printf("⚠️  No models available for provider %s\n", cli.Provider)
		fmt.Printf("💡 This usually means:\n")
		fmt.Printf("   • API key doesn't have access to any models\n")
		fmt.Printf("   • Provider has no compatible models\n")
		fmt.Printf("   • Service is temporarily unavailable\n\n")

		if !askYesNo("Would you like to continue anyway? (default models will be used)") {
			return fmt.Errorf("no models available")
		}
		return nil
	}

	// Display available models
	fmt.Printf("✅ Found %d available models for %s:\n", len(models), cli.Provider)
	for i, model := range models {
		if i < 5 { // Show first 5 models
			fmt.Printf("   • %s\n", model.Name)
		} else if i == 5 {
			fmt.Printf("   • ... and %d more models\n", len(models)-5)
			break
		}
	}

	// Ask if user wants to configure model-specific settings
	if askYesNo("\nWould you like to configure model-specific API keys or settings?") {
		return promptModelSpecificConfig(cli, models, config)
	}

	fmt.Printf("✅ Using default configuration for all models\n\n")
	return nil
}

// Prompt for model-specific configuration
func promptModelSpecificConfig(cli *CLIConfig, models []ModelInfo, config *InteractiveConfig) error {
	fmt.Printf("\n🔧 Model-Specific Configuration:\n")
	fmt.Printf("   Some models may require different API keys or settings\n\n")

	// For now, we'll focus on the main models that users typically want to configure
	importantModels := []string{"kimi-k2:latest", "kimi-k2:coding", "deepseek-r1:14b"}

	for _, modelName := range importantModels {
		// Check if this model is available
		var foundModel *ModelInfo
		for _, model := range models {
			if model.Name == modelName {
				foundModel = &model
				break
			}
		}

		if foundModel == nil {
			continue // Skip if model not available
		}

		fmt.Printf("🤖 Configure %s?\n", modelName)
		if askYesNo(fmt.Sprintf("   Would you like to set specific configuration for %s?", modelName)) {
			if err := promptModelSpecificAPIKey(cli, modelName, config); err != nil {
				return err
			}
		}
	}

	return nil
}

// Prompt for model-specific API key
func promptModelSpecificAPIKey(cli *CLIConfig, modelName string, config *InteractiveConfig) error {
	fmt.Printf("\n🔑 API Key for %s:\n", modelName)
	fmt.Printf("   Current provider: %s\n", cli.Provider)
	fmt.Printf("   Default API key: %s...%s\n", cli.getAPIKey()[:8], cli.getAPIKey()[len(cli.getAPIKey())-4:])

	if askYesNo("   Use a different API key for this model?") {
		for attempt := 1; attempt <= config.MaxRetries; attempt++ {
			fmt.Printf("\nEnter API key for %s: ", modelName)

			apiKey, err := readMaskedInput()
			if err != nil {
				fmt.Printf("❌ Failed to read API key: %v\n", err)
				if !askRetry(attempt, config.MaxRetries) {
					return fmt.Errorf("API key entry cancelled")
				}
				continue
			}

			if len(apiKey) == 0 {
				fmt.Printf("❌ API key cannot be empty\n")
				if !askRetry(attempt, config.MaxRetries) {
					return fmt.Errorf("API key entry cancelled")
				}
				continue
			}

			// Validate the API key format
			if err := validateAPIKeyFormat(cli.Provider, apiKey); err != nil {
				fmt.Printf("❌ Invalid API key format: %v\n", err)
				if !askRetry(attempt, config.MaxRetries) {
					return fmt.Errorf("API key validation cancelled")
				}
				continue
			}

			// Test the API key
			fmt.Printf("🔍 Testing API key for %s...", modelName)
			if err := testModelSpecificAPIKey(cli.Provider, modelName, apiKey, config); err != nil {
				fmt.Printf("\n❌ API key test failed: %v\n", err)
				if !askRetry(attempt, config.MaxRetries) {
					return fmt.Errorf("API key testing cancelled")
				}
				continue
			}

			fmt.Printf("\n✅ API key validated for %s\n", modelName)
			// TODO: Store model-specific API key in configuration
			break
		}
	}

	return nil
}

// Prompt for optional configuration
func promptOptionalConfig(cli *CLIConfig, config *InteractiveConfig) error {
	fmt.Printf("⚙️  Optional Configuration:\n")
	
	// Ask about custom port
	if askYesNo("Would you like to use a custom port? (default: 11434)") {
		for attempt := 1; attempt <= config.MaxRetries; attempt++ {
			fmt.Printf("Enter port number: ")
			reader := bufio.NewReader(os.Stdin)
			input, err := reader.ReadString('\n')
			if err != nil {
				return fmt.Errorf("failed to read port: %v", err)
			}

			port := strings.TrimSpace(input)
			if port == "" {
				port = "11434"
			}

			// Validate port
			portNum, err := strconv.Atoi(port)
			if err != nil || portNum < 1 || portNum > 65535 {
				fmt.Printf("❌ Invalid port number. Please enter a number between 1 and 65535.\n")
				if !askRetry(attempt, config.MaxRetries) {
					break
				}
				continue
			}

			cli.Port = port
			fmt.Printf("✅ Port set to: %s\n", port)
			break
		}
	}

	// Ask about custom host
	if askYesNo("Would you like to use a custom host? (default: 0.0.0.0)") {
		fmt.Printf("Enter host address: ")
		reader := bufio.NewReader(os.Stdin)
		input, err := reader.ReadString('\n')
		if err != nil {
			return fmt.Errorf("failed to read host: %v", err)
		}

		host := strings.TrimSpace(input)
		if host != "" {
			cli.Host = host
			fmt.Printf("✅ Host set to: %s\n", host)
		}
	}

	// Ask about verbose logging
	if askYesNo("Enable verbose logging for debugging?") {
		cli.Verbose = true
		fmt.Printf("✅ Verbose logging enabled\n")
	}

	fmt.Printf("\n")
	return nil
}

// Validate complete interactive configuration
func validateInteractiveConfig(cli *CLIConfig, config *InteractiveConfig) error {
	// Apply configuration
	if err := cli.apply(); err != nil {
		return fmt.Errorf("configuration error: %v", err)
	}

	// Test complete setup
	fmt.Printf("Testing complete configuration...\n")
	
	// Load model mappings
	mappings, err := loadModelMappings(cli.ModelsFile)
	if err != nil {
		return fmt.Errorf("failed to load model mappings: %v", err)
	}

	fmt.Printf("✅ Loaded %d model mappings\n", len(mappings))
	fmt.Printf("✅ Server will start on %s:%s\n", cli.Host, cli.Port)
	fmt.Printf("✅ Provider: %s\n", cli.Provider)

	return nil
}

// Save interactive configuration
func saveInteractiveConfig(cli *CLIConfig) error {
	// Create persistent configuration from CLI
	persistentConfig := createPersistentConfigFromCLI(cli)

	// Validate configuration
	if err := validatePersistentConfig(persistentConfig); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	// Save to persistent storage
	if err := savePersistentConfig(persistentConfig); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	return nil
}

// Test API key with timeout and progress
func testAPIKeyInteractive(provider, apiKey string, config *InteractiveConfig) error {
	testConfig := &Config{
		OpenRouterAPIKey:  apiKey,
		OpenRouterBaseURL: getProviderBaseURL(provider),
		RequestTimeout:    config.TimeoutSeconds,
		MaxRetries:        1,
		EnableRequestLog:  false,
		EnableResponseLog: false,
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(config.TimeoutSeconds)*time.Second)
	defer cancel()

	// Test with progress indicator
	done := make(chan error, 1)
	go func() {
		done <- testAPIKey(testConfig, provider)
	}()

	// Show progress
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	dots := 0
	for {
		select {
		case err := <-done:
			return err
		case <-ticker.C:
			dots = (dots + 1) % 4
			fmt.Printf("\r🔍 Testing API key connectivity%s", strings.Repeat(".", dots))
		case <-ctx.Done():
			return fmt.Errorf("API key test timed out after %d seconds", config.TimeoutSeconds)
		}
	}
}

// Read masked input for passwords/API keys
func readMaskedInput() (string, error) {
	fd := int(syscall.Stdin)
	if !term.IsTerminal(fd) {
		// Fallback for non-terminal input
		reader := bufio.NewReader(os.Stdin)
		input, err := reader.ReadString('\n')
		return strings.TrimSpace(input), err
	}

	// Provide instructions for paste operations
	fmt.Printf("(Paste your API key and press Enter - input will be hidden)\n")
	fmt.Printf("Key: ")

	// Use term.ReadPassword which handles paste operations better
	bytes, err := term.ReadPassword(fd)
	if err != nil {
		// If ReadPassword fails, try fallback method
		fmt.Printf("\nMasked input failed, trying plain text input...\n")
		fmt.Printf("Key: ")
		reader := bufio.NewReader(os.Stdin)
		input, err := reader.ReadString('\n')
		if err != nil {
			return "", fmt.Errorf("failed to read input: %v", err)
		}
		return strings.TrimSpace(input), nil
	}

	// Trim any whitespace that might have been pasted
	apiKey := strings.TrimSpace(string(bytes))

	// Handle common paste issues - remove any newlines or carriage returns
	apiKey = strings.ReplaceAll(apiKey, "\n", "")
	apiKey = strings.ReplaceAll(apiKey, "\r", "")
	apiKey = strings.ReplaceAll(apiKey, "\t", "")

	return apiKey, nil
}

// Ask yes/no question
func askYesNo(question string) bool {
	fmt.Printf("%s (y/n): ", question)
	reader := bufio.NewReader(os.Stdin)
	input, err := reader.ReadString('\n')
	if err != nil {
		return false
	}

	input = strings.ToLower(strings.TrimSpace(input))
	return input == "y" || input == "yes"
}

// Ask if user wants to retry
func askRetry(attempt, maxRetries int) bool {
	if attempt >= maxRetries {
		fmt.Printf("Maximum retry attempts (%d) reached.\n", maxRetries)
		return false
	}

	return askYesNo(fmt.Sprintf("Would you like to try again? (%d/%d attempts used)", attempt, maxRetries))
}

// Get expected API key format for provider
func getAPIKeyFormat(provider string) string {
	switch provider {
	case "openrouter":
		return "sk-or-v1-..."
	case "openai":
		return "sk-..."
	case "anthropic":
		return "sk-ant-..."
	case "moonshot-direct":
		return "sk-... (e.g., sk-0RbbR9nn2wZIe15VaOaykYWYAOAuFt9WNQpYrGN3eC5btl7V)"
	default:
		return "varies by provider"
	}
}

// Get available models for a provider
func getAvailableModelsForProvider(provider, apiKey string) ([]ModelInfo, error) {
	if apiKey == "" {
		return nil, fmt.Errorf("API key required - no local models available")
	}

	// Create a test configuration
	testConfig := &Config{
		OpenRouterAPIKey:  apiKey,
		OpenRouterBaseURL: getProviderBaseURL(provider),
		RequestTimeout:    10,
		MaxRetries:        1,
		EnableRequestLog:  false,
		EnableResponseLog: false,
	}

	// Test API connectivity first
	client := NewOpenRouterClient(testConfig)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Try to get models list to verify API key works
	_, err := client.GetModels(ctx)
	if err != nil {
		return nil, fmt.Errorf("API key validation failed: %v", err)
	}

	// Load model mappings to see what models are available
	mappings, err := loadModelMappings("models.json")
	if err != nil {
		return nil, fmt.Errorf("failed to load model mappings: %v", err)
	}

	var models []ModelInfo
	for ollamaName, mapping := range mappings {
		// Only include models that match the current provider or are compatible
		if isModelCompatibleWithProvider(mapping, provider) {
			models = append(models, ModelInfo{
				Name:        ollamaName,
				Provider:    mapping.Provider,
				Description: mapping.Description,
				Available:   true,
			})
		}
	}

	return models, nil
}

// Check if a model is compatible with the provider
func isModelCompatibleWithProvider(mapping ModelMapping, provider string) bool {
	// For OpenRouter, all models are potentially available
	if provider == "openrouter" {
		return true
	}

	// For direct providers, only show models that match the provider
	switch provider {
	case "moonshot-direct":
		return mapping.Provider == "moonshot" || strings.Contains(strings.ToLower(mapping.OpenRouterName), "moonshot")
	case "anthropic":
		return mapping.Provider == "anthropic" || strings.Contains(strings.ToLower(mapping.OpenRouterName), "claude")
	case "openai":
		return mapping.Provider == "openai" || strings.Contains(strings.ToLower(mapping.OpenRouterName), "gpt")
	default:
		return mapping.Provider == provider
	}
}

// Test model-specific API key
func testModelSpecificAPIKey(provider, modelName, apiKey string, config *InteractiveConfig) error {
	testConfig := &Config{
		OpenRouterAPIKey:  apiKey,
		OpenRouterBaseURL: getProviderBaseURL(provider),
		RequestTimeout:    config.TimeoutSeconds,
		MaxRetries:        1,
		EnableRequestLog:  false,
		EnableResponseLog: false,
	}

	// Test with progress indicator
	done := make(chan error, 1)
	go func() {
		done <- testAPIKey(testConfig, provider)
	}()

	// Show progress
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	dots := 0
	for {
		select {
		case err := <-done:
			return err
		case <-ticker.C:
			dots = (dots + 1) % 4
			fmt.Printf("\r🔍 Testing API key for %s%s", modelName, strings.Repeat(".", dots))
		case <-time.After(time.Duration(config.TimeoutSeconds) * time.Second):
			return fmt.Errorf("API key test timed out after %d seconds", config.TimeoutSeconds)
		}
	}
}

// Show graceful exit message
func showGracefulExit(reason string) {
	fmt.Printf("\n❌ Setup cancelled: %s\n\n", reason)
	fmt.Printf("💡 You can also use non-interactive mode:\n")
	fmt.Printf("   fake-ollama --api-key your-key --provider openrouter\n")
	fmt.Printf("   fake-ollama --help  # for all options\n\n")
	fmt.Printf("🔧 Troubleshooting:\n")
	fmt.Printf("   • Get API keys: https://openrouter.ai/keys\n")
	fmt.Printf("   • Check network connectivity\n")
	fmt.Printf("   • Use --dry-run to test configuration\n\n")
	fmt.Printf("📋 Remember: fake-ollama requires API keys - no local models available\n\n")
}
