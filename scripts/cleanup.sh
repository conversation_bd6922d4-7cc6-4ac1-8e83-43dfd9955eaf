#!/bin/bash

# Cleanup Action Script
# Implements engineering directives for code hygiene and quality

set -e

echo "🧹 Starting cleanup action..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    print_error "go.mod not found. Please run this script from the project root."
    exit 1
fi

print_status "Checking project structure..."

# Step 1: Remove unused imports and dead code
print_status "Step 1: Cleaning up imports and dependencies..."

# Run go mod tidy to clean up dependencies
print_status "Running go mod tidy..."
go mod tidy
print_success "Dependencies cleaned up"

# Format code with gofmt
print_status "Running gofmt..."
gofmt -w .
print_success "Code formatted with gofmt"

# Organize imports with goimports
print_status "Running goimports..."
if command -v goimports &> /dev/null; then
    goimports -w .
    print_success "Imports organized with goimports"
else
    print_warning "goimports not found, installing..."
    go install golang.org/x/tools/cmd/goimports@latest
    goimports -w .
    print_success "Imports organized with goimports"
fi

# Step 2: Check for Chinese comments and text
print_status "Step 2: Checking for Chinese text..."

chinese_files=()
while IFS= read -r -d '' file; do
    if [[ "$file" == *.go ]]; then
        if python3 -c "
import re
with open('$file', 'r', encoding='utf-8') as f:
    content = f.read()
    if re.search(r'[\u4e00-\u9fff]', content):
        exit(1)
" 2>/dev/null; then
            continue
        else
            chinese_files+=("$file")
        fi
    fi
done < <(find . -name "*.go" -print0)

if [ ${#chinese_files[@]} -gt 0 ]; then
    print_warning "Found Chinese text in the following files:"
    for file in "${chinese_files[@]}"; do
        echo "  • $file"
    done
    print_warning "Please replace Chinese comments with English equivalents"
else
    print_success "No Chinese text found in Go files"
fi

# Step 3: Validate file size limits (500 lines)
print_status "Step 3: Validating file size limits..."

violations=()
warnings=()

while IFS= read -r -d '' file; do
    if [[ "$file" == *.go ]]; then
        lines=$(wc -l < "$file")
        if [ "$lines" -gt 500 ]; then
            violations+=("$file: $lines lines (exceeds 500-line limit)")
        elif [ "$lines" -gt 400 ]; then
            warnings+=("$file: $lines lines (approaching 500-line limit)")
        fi
    fi
done < <(find . -name "*.go" -print0)

# Report violations
if [ ${#violations[@]} -gt 0 ]; then
    print_error "Code hygiene violations found:"
    for violation in "${violations[@]}"; do
        echo "  • $violation"
    done
    echo ""
    print_error "Please refactor these files to stay under 500 lines"
    exit 1
fi

# Report warnings
if [ ${#warnings[@]} -gt 0 ]; then
    print_warning "Files approaching 500-line limit:"
    for warning in "${warnings[@]}"; do
        echo "  • $warning"
    done
    echo ""
fi

print_success "All files within 500-line limit"

# Step 4: Run static analysis
print_status "Step 4: Running static analysis..."

# Run go vet
print_status "Running go vet..."
if go vet ./...; then
    print_success "go vet passed"
else
    print_error "go vet found issues"
    exit 1
fi

# Run golangci-lint if available
if command -v golangci-lint &> /dev/null; then
    print_status "Running golangci-lint..."
    if golangci-lint run; then
        print_success "golangci-lint passed"
    else
        print_warning "golangci-lint found issues (non-blocking)"
    fi
else
    print_warning "golangci-lint not found, skipping advanced linting"
fi

# Step 5: Test the build
print_status "Step 5: Testing build..."

if go build -o fake-ollama; then
    print_success "Build successful"
    rm -f fake-ollama  # Clean up binary
else
    print_error "Build failed"
    exit 1
fi

# Step 6: Run tests
print_status "Step 6: Running tests..."

if go test -v ./...; then
    print_success "All tests passed"
else
    print_error "Some tests failed"
    exit 1
fi

# Step 7: Test pre-commit hook
print_status "Step 7: Testing pre-commit hook..."

if [ -f ".githooks/pre-commit" ]; then
    if .githooks/pre-commit; then
        print_success "Pre-commit hook passed"
    else
        print_error "Pre-commit hook failed"
        exit 1
    fi
else
    print_warning "Pre-commit hook not found at .githooks/pre-commit"
fi

# Step 8: Generate summary report
print_status "Step 8: Generating cleanup summary..."

echo ""
echo "📊 Cleanup Summary Report"
echo "========================="

# Count lines of code
total_lines=0
file_count=0
while IFS= read -r -d '' file; do
    if [[ "$file" == *.go ]]; then
        lines=$(wc -l < "$file")
        total_lines=$((total_lines + lines))
        file_count=$((file_count + 1))
        printf "%-25s %3d lines\n" "$(basename "$file")" "$lines"
    fi
done < <(find . -name "*.go" -print0)

echo "========================="
printf "%-25s %3d lines\n" "Total ($file_count files)" "$total_lines"
printf "%-25s %3d lines\n" "Average per file" $((total_lines / file_count))
echo ""

# Check dependencies
print_status "Dependencies:"
go list -m all | head -10
echo ""

print_success "🎉 Cleanup action completed successfully!"
print_status "Next steps:"
echo "  • Review any warnings above"
echo "  • Commit your changes"
echo "  • Run the documentation update script"

exit 0
