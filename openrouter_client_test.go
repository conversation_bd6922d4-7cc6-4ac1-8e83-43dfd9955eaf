package main

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// Test OpenRouter client basic functionality
func TestNewOpenRouterClient(t *testing.T) {
	config := &Config{
		OpenRouterAPIKey:  "test-key",
		OpenRouterBaseURL: "https://openrouter.ai/api/v1",
		RequestTimeout:    30,
		MaxRetries:        3,
	}

	client := NewOpenRouterClient(config)
	
	if client == nil {
		t.Fatal("Expected client to be created, got nil")
	}
	
	if client.config != config {
		t.Error("Expected client config to match provided config")
	}
	
	if client.httpClient == nil {
		t.Error("Expected HTTP client to be initialized")
	}
}

// Test request transformation from Ollama to OpenRouter format
func TestTransformChatRequest(t *testing.T) {
	tests := []struct {
		name     string
		input    ChatRequest
		model    string
		expected OpenRouterChatRequest
	}{
		{
			name: "basic chat request",
			input: ChatRequest{
				Model: "deepseek-r1:14b",
				Messages: []Message{
					{Role: "user", Content: "Hello"},
				},
			},
			model: "deepseek/deepseek-r1",
			expected: OpenRouterChatRequest{
				Model: "deepseek/deepseek-r1",
				Messages: []OpenRouterMessage{
					{Role: "user", Content: "Hello"},
				},
			},
		},
		{
			name: "streaming chat request",
			input: ChatRequest{
				Model: "deepseek-r1:32b",
				Messages: []Message{
					{Role: "system", Content: "You are helpful"},
					{Role: "user", Content: "Hi there"},
				},
				Stream: boolPtr(true),
			},
			model: "openai/gpt-4o",
			expected: OpenRouterChatRequest{
				Model:  "openai/gpt-4o",
				Stream: boolPtr(true),
				Messages: []OpenRouterMessage{
					{Role: "system", Content: "You are helpful"},
					{Role: "user", Content: "Hi there"},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := transformChatRequest(tt.input, tt.model)
			
			if result.Model != tt.expected.Model {
				t.Errorf("Expected model %s, got %s", tt.expected.Model, result.Model)
			}
			
			if len(result.Messages) != len(tt.expected.Messages) {
				t.Errorf("Expected %d messages, got %d", len(tt.expected.Messages), len(result.Messages))
			}
			
			for i, msg := range result.Messages {
				if msg.Role != tt.expected.Messages[i].Role {
					t.Errorf("Message %d: expected role %s, got %s", i, tt.expected.Messages[i].Role, msg.Role)
				}
				if msg.Content != tt.expected.Messages[i].Content {
					t.Errorf("Message %d: expected content %s, got %s", i, tt.expected.Messages[i].Content, msg.Content)
				}
			}
		})
	}
}

// Test model mapping functionality
func TestGetOpenRouterModel(t *testing.T) {
	// Mock model mappings for testing
	testMappings := []ModelMapping{
		{
			OllamaName:     "deepseek-r1:14b",
			OpenRouterName: "deepseek/deepseek-r1",
			Provider:       "deepseek",
		},
		{
			OllamaName:     "deepseek-r1:32b",
			OpenRouterName: "openai/gpt-4o",
			Provider:       "openai",
		},
	}

	tests := []struct {
		name        string
		ollamaModel string
		expected    string
		expectError bool
	}{
		{
			name:        "valid model mapping",
			ollamaModel: "deepseek-r1:14b",
			expected:    "deepseek/deepseek-r1",
			expectError: false,
		},
		{
			name:        "another valid mapping",
			ollamaModel: "deepseek-r1:32b",
			expected:    "openai/gpt-4o",
			expectError: false,
		},
		{
			name:        "invalid model",
			ollamaModel: "nonexistent:1b",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getOpenRouterModel(tt.ollamaModel, testMappings)
			
			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

// Test HTTP client with mock server
func TestOpenRouterClient_ChatCompletion(t *testing.T) {
	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request headers
		if r.Header.Get("Authorization") != "Bearer test-key" {
			t.Error("Expected Authorization header with Bearer token")
		}
		
		if r.Header.Get("Content-Type") != "application/json" {
			t.Error("Expected Content-Type: application/json")
		}
		
		// Mock response
		response := OpenRouterChatResponse{
			ID:      "test-id",
			Object:  "chat.completion",
			Created: time.Now().Unix(),
			Model:   "deepseek/deepseek-r1",
			Choices: []OpenRouterChoice{
				{
					Index: 0,
					Message: OpenRouterMessage{
						Role:    "assistant",
						Content: "Hello! How can I help you?",
					},
					FinishReason: "stop",
				},
			},
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	config := &Config{
		OpenRouterAPIKey:  "test-key",
		OpenRouterBaseURL: server.URL,
		RequestTimeout:    30,
		MaxRetries:        1,
	}

	client := NewOpenRouterClient(config)
	
	request := OpenRouterChatRequest{
		Model: "deepseek/deepseek-r1",
		Messages: []OpenRouterMessage{
			{Role: "user", Content: "Hello"},
		},
	}

	ctx := context.Background()
	response, err := client.ChatCompletion(ctx, request)
	
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	
	if response.ID != "test-id" {
		t.Errorf("Expected ID 'test-id', got %s", response.ID)
	}
	
	if len(response.Choices) != 1 {
		t.Errorf("Expected 1 choice, got %d", len(response.Choices))
	}
	
	if response.Choices[0].Message.Content != "Hello! How can I help you?" {
		t.Errorf("Unexpected response content: %s", response.Choices[0].Message.Content)
	}
}


