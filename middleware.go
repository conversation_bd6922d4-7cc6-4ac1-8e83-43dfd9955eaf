package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"

	"github.com/gin-gonic/gin"
)

// Middleware types and functions
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	return w.body.Write(b) // Only write to buffer, not to response
}

// Complete middleware function implementation
func ListMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Save original Writer
		originalWriter := c.Writer

		// Create custom ResponseWriter to capture response
		blw := &bodyLogWriter{
			body:           bytes.NewBufferString(""),
			ResponseWriter: c.Writer,
		}
		c.Writer = blw

		c.Next()

		// Restore original Writer
		c.Writer = originalWriter

		// Parse original response
		var listResponse ListResponse
		if err := json.Unmarshal(blw.body.Bytes(), &listResponse); err != nil {
			c.<PERSON>(http.StatusInternalServerError, NewError(http.StatusInternalServerError, "invalid response format"))
			return
		}

		// Convert to OpenAI format
		models := make([]OpenAIModel, len(listResponse.Models))
		for i, m := range listResponse.Models {
			models[i] = OpenAIModel{
				ID:      m.Name,
				Object:  "model",
				Created: m.ModifiedAt.Unix(),
				OwnedBy: "library",
			}
		}

		response := OpenAIModelList{
			Object: "list",
			Data:   models,
		}

		// Write converted response
		c.JSON(http.StatusOK, response)
	}
}

func ChatMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate unique ID
		id := fmt.Sprintf("chatcmpl-%d", rand.Intn(999))
		c.Set("chat_id", id)
		c.Next()
	}
}

func CompletionsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate unique ID
		id := fmt.Sprintf("cmpl-%d", rand.Intn(999))
		c.Set("completion_id", id)
		c.Next()
	}
}

func EmbeddingsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Input any    `json:"input"`
			Model string `json:"model"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, NewError(http.StatusBadRequest, err.Error()))
			return
		}

		// Validate input
		if req.Input == nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, NewError(http.StatusBadRequest, "input is required"))
			return
		}

		c.Next()
	}
}

func RetrieveMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		modelName := c.Param("model")

		// Construct request body
		var b bytes.Buffer
		if err := json.NewEncoder(&b).Encode(map[string]string{
			"name": modelName,
		}); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, NewError(http.StatusInternalServerError, err.Error()))
			return
		}

		// Replace request body
		c.Request.Body = io.NopCloser(&b)

		c.Next()
	}
}
