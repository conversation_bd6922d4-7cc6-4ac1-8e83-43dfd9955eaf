package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
)

// Test the integrated chat handler with mock OpenRouter server
func TestChatHandlerWithOpenRouter_Integration(t *testing.T) {
	// Set up mock OpenRouter server
	mockOpenRouter := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request headers
		if r.Header.Get("Authorization") != "Bearer test-key" {
			t.Error("Expected Authorization header with Bearer token")
		}
		
		// Mock OpenRouter response
		response := OpenRouterChatResponse{
			ID:      "chatcmpl-test",
			Object:  "chat.completion",
			Created: time.Now().Unix(),
			Model:   "deepseek/deepseek-r1",
			Choices: []OpenRouterChoice{
				{
					Index: 0,
					Message: OpenRouterMessage{
						Role:    "assistant",
						Content: "This is a real response from OpenRouter API!",
					},
					FinishReason: "stop",
				},
			},
		}
		
		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer mockOpenRouter.Close()

	// Set up test configuration
	testConfig := &Config{
		OpenRouterAPIKey:  "test-key",
		OpenRouterBaseURL: mockOpenRouter.URL,
		RequestTimeout:    30,
		MaxRetries:        1,
		EnableRequestLog:  false,
		EnableResponseLog: false,
	}

	// Initialize test environment
	appConfig = testConfig
	modelMappings = map[string]ModelMapping{
		"deepseek-r1:14b": {
			OllamaName:     "deepseek-r1:14b",
			OpenRouterName: "deepseek/deepseek-r1",
			Provider:       "deepseek",
		},
	}
	initOpenRouterClient()

	// Set up Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/chat", chatHandlerWithOpenRouter)

	// Create test request
	chatReq := ChatRequest{
		Model: "deepseek-r1:14b",
		Messages: []Message{
			{Role: "user", Content: "Hello, how are you?"},
		},
	}

	reqBody, _ := json.Marshal(chatReq)
	req := httptest.NewRequest("POST", "/api/chat", bytes.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	var response ChatResponse
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify response content
	if response.Model != "deepseek-r1:14b" {
		t.Errorf("Expected model 'deepseek-r1:14b', got '%s'", response.Model)
	}

	if len(response.Choices) != 1 {
		t.Errorf("Expected 1 choice, got %d", len(response.Choices))
	}

	expectedContent := "This is a real response from OpenRouter API!"
	if response.Choices[0].Delta.Content != expectedContent {
		t.Errorf("Expected content '%s', got '%s'", expectedContent, response.Choices[0].Delta.Content)
	}

	// Verify it's not a fallback response
	if response.Choices[0].Delta.Content == "[FALLBACK]" {
		t.Error("Response should not be a fallback")
	}
}

// Test fallback behavior when OpenRouter is unavailable
func TestChatHandlerWithOpenRouter_Fallback(t *testing.T) {
	// Set up test configuration with invalid URL
	testConfig := &Config{
		OpenRouterAPIKey:  "test-key",
		OpenRouterBaseURL: "http://invalid-url-that-does-not-exist",
		RequestTimeout:    1, // Short timeout to trigger fallback quickly
		MaxRetries:        1,
		EnableRequestLog:  false,
		EnableResponseLog: false,
	}

	// Initialize test environment
	appConfig = testConfig
	modelMappings = map[string]ModelMapping{
		"deepseek-r1:14b": {
			OllamaName:     "deepseek-r1:14b",
			OpenRouterName: "deepseek/deepseek-r1",
			Provider:       "deepseek",
		},
	}
	initOpenRouterClient()

	// Set up Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/chat", chatHandlerWithOpenRouter)

	// Create test request
	chatReq := ChatRequest{
		Model: "deepseek-r1:14b",
		Messages: []Message{
			{Role: "user", Content: "Hello, how are you?"},
		},
	}

	reqBody, _ := json.Marshal(chatReq)
	req := httptest.NewRequest("POST", "/api/chat", bytes.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	var response ChatResponse
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify it's a fallback response
	content := response.Choices[0].Delta.Content
	if content == "" {
		t.Error("Expected fallback content, got empty string")
	}

	// Should contain fallback indicator
	if len(content) > 0 && content[:10] != "[FALLBACK]" {
		t.Errorf("Expected fallback response to start with '[FALLBACK]', got: %s", content)
	}
}

// Test generate handler integration
func TestGenerateHandlerWithOpenRouter_Integration(t *testing.T) {
	// Set up mock OpenRouter server
	mockOpenRouter := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Mock OpenRouter completion response
		response := OpenRouterCompletionResponse{
			ID:      "cmpl-test",
			Object:  "text_completion",
			Created: time.Now().Unix(),
			Model:   "deepseek/deepseek-r1",
			Choices: []OpenRouterCompletionChoice{
				{
					Index:        0,
					Text:         "This is a completion from OpenRouter!",
					FinishReason: "stop",
				},
			},
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer mockOpenRouter.Close()

	// Set up test configuration
	testConfig := &Config{
		OpenRouterAPIKey:  "test-key",
		OpenRouterBaseURL: mockOpenRouter.URL,
		RequestTimeout:    30,
		MaxRetries:        1,
		EnableRequestLog:  false,
		EnableResponseLog: false,
	}

	// Initialize test environment
	appConfig = testConfig
	modelMappings = map[string]ModelMapping{
		"deepseek-r1:14b": {
			OllamaName:     "deepseek-r1:14b",
			OpenRouterName: "deepseek/deepseek-r1",
			Provider:       "deepseek",
		},
	}
	initOpenRouterClient()

	// Set up Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/generate", generateHandlerWithOpenRouter)

	// Create test request
	genReq := GenerateRequest{
		Model:  "deepseek-r1:14b",
		Prompt: "Complete this sentence: The weather today is",
	}

	reqBody, _ := json.Marshal(genReq)
	req := httptest.NewRequest("POST", "/api/generate", bytes.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	var response GenerateResponse
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify response content
	if response.Model != "deepseek-r1:14b" {
		t.Errorf("Expected model 'deepseek-r1:14b', got '%s'", response.Model)
	}

	expectedContent := "This is a completion from OpenRouter!"
	if response.Response != expectedContent {
		t.Errorf("Expected response '%s', got '%s'", expectedContent, response.Response)
	}

	if !response.Done {
		t.Error("Expected response to be marked as done")
	}
}

// Test invalid model handling
func TestChatHandlerWithOpenRouter_InvalidModel(t *testing.T) {
	// Set up test configuration
	testConfig := &Config{
		OpenRouterAPIKey:  "test-key",
		OpenRouterBaseURL: "http://localhost:8080",
		RequestTimeout:    30,
		MaxRetries:        1,
	}

	// Initialize test environment with empty model mappings
	appConfig = testConfig
	modelMappings = map[string]ModelMapping{} // Empty mappings
	initOpenRouterClient()

	// Set up Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/chat", chatHandlerWithOpenRouter)

	// Create test request with invalid model
	chatReq := ChatRequest{
		Model: "invalid-model:1b",
		Messages: []Message{
			{Role: "user", Content: "Hello"},
		},
	}

	reqBody, _ := json.Marshal(chatReq)
	req := httptest.NewRequest("POST", "/api/chat", bytes.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify error response
	if w.Code != http.StatusNotFound {
		t.Errorf("Expected status 404, got %d", w.Code)
	}

	var errorResponse ErrorResponse
	if err := json.Unmarshal(w.Body.Bytes(), &errorResponse); err != nil {
		t.Fatalf("Failed to unmarshal error response: %v", err)
	}

	if errorResponse.Error.Message == "" {
		t.Error("Expected error message, got empty string")
	}
}
