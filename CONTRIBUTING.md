# Contributing to fake-ollama

Thank you for your interest in contributing to fake-ollama! This document provides guidelines and information for contributors.

## Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow. Please be respectful and constructive in all interactions.

## Getting Started

### Prerequisites

- Go 1.21 or later
- Git
- OpenRouter API key (for testing)
- Docker (optional, for container testing)

### Development Setup

1. **Fork and Clone**
   ```bash
   git fork https://github.com/spoonnotfound/fake-ollama.git
   git clone https://github.com/YOUR_USERNAME/fake-ollama.git
   cd fake-ollama
   ```

2. **Install Dependencies**
   ```bash
   go mod tidy
   ```

3. **Set Up Pre-commit Hooks**
   ```bash
   git config core.hooksPath .githooks
   chmod +x .githooks/pre-commit
   ```

4. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your OpenRouter API key
   export OPENROUTER_API_KEY="your-api-key-here"
   ```

5. **Verify Setup**
   ```bash
   go build -o fake-ollama
   go test -v ./...
   ./fake-ollama  # Should start successfully
   ```

## Development Workflow

### Branch Strategy

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/description` - Feature development
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates

### Making Changes

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow the code style guidelines below
   - Respect the 500-line file limit
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Your Changes**
   ```bash
   go test -v ./...
   go test -race ./...
   go build -o fake-ollama
   ```

4. **Run Quality Checks**
   ```bash
   .githooks/pre-commit  # Validates file sizes
   gofmt -w .
   go run golang.org/x/tools/cmd/goimports@latest -w .
   ```

5. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

## Code Style Guidelines

### File Organization

**500-Line Maximum Rule**: No file may exceed 500 lines (including comments and blanks).

**When approaching the limit:**
- Extract private helpers into separate files (`*_helpers.go`, `*_utils.go`)
- Split by functional responsibility
- Remove code duplication
- Use early returns to reduce nesting

**Current Architecture:**
```
├── main.go              (103 lines) - Application entry point
├── config.go            (166 lines) - Configuration management
├── models.go            (137 lines) - Model definitions
├── types.go             (143 lines) - Type definitions
├── utils.go             (73 lines)  - Utility functions
├── handlers.go          (250 lines) - Main API handlers
├── handlers_misc.go     (170 lines) - Secondary handlers
├── middleware.go        (128 lines) - Middleware functions
```

### Go Standards

**Formatting:**
```bash
gofmt -w .
go run golang.org/x/tools/cmd/goimports@latest -w .
```

**Naming Conventions:**
- Use descriptive names that reveal intent
- Avoid abbreviations unless widely understood
- Use camelCase for variables and functions
- Use PascalCase for exported types and functions

**Error Handling:**
```go
// Good - wrap errors with context
if err != nil {
    return fmt.Errorf("failed to load config: %w", err)
}

// Bad - ignore or return raw errors
if err != nil {
    return err
}
```

**Comments:**
- Use English only
- Comment the "why" not the "what"
- Add package-level documentation
- Document exported functions and types

### Testing Guidelines

**Test Structure:**
```go
func TestFunctionName(t *testing.T) {
    tests := []struct {
        name     string
        input    InputType
        expected OutputType
        wantErr  bool
    }{
        {
            name:     "valid input",
            input:    validInput,
            expected: expectedOutput,
            wantErr:  false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := FunctionName(tt.input)
            if tt.wantErr {
                assert.Error(t, err)
                return
            }
            assert.NoError(t, err)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

**Test Coverage:**
- Aim for >90% code coverage
- Test both success and error paths
- Include edge cases and boundary conditions
- Use table-driven tests for multiple scenarios

## Commit Message Format

Use [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(config): add OpenRouter API key validation
fix(handlers): resolve streaming response memory leak
docs(readme): update configuration examples
refactor(models): split large file into focused modules
```

## Pull Request Process

### Before Submitting

1. **Ensure Quality**
   - All tests pass: `go test -v ./...`
   - No race conditions: `go test -race ./...`
   - Code builds: `go build -o fake-ollama`
   - Pre-commit hook passes: `.githooks/pre-commit`

2. **Update Documentation**
   - Update README if adding features
   - Add/update code comments
   - Update CHANGELOG.md
   - Include usage examples

3. **Self Review**
   - Review your own changes
   - Ensure no debug code or temporary files
   - Verify all files are under 500 lines
   - Check for proper error handling

### Submitting

1. **Create Pull Request**
   - Use descriptive title and description
   - Reference related issues
   - Include testing instructions
   - Add screenshots for UI changes

2. **PR Template**
   ```markdown
   ## Description
   Brief description of changes
   
   ## Type of Change
   - [ ] Bug fix
   - [ ] New feature
   - [ ] Documentation update
   - [ ] Refactoring
   
   ## Testing
   - [ ] Tests pass locally
   - [ ] Added tests for new functionality
   - [ ] Manual testing completed
   
   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] All files under 500-line limit
   - [ ] Documentation updated
   - [ ] CHANGELOG.md updated
   ```

### Review Process

1. **Automated Checks**
   - GitHub Actions CI/CD pipeline
   - Code hygiene validation
   - Test execution
   - Build verification

2. **Manual Review**
   - Code quality and style
   - Architecture and design
   - Test coverage and quality
   - Documentation completeness

3. **Approval and Merge**
   - Requires at least one approval
   - All checks must pass
   - Squash and merge preferred

## Issue Reporting

### Bug Reports

Use the bug report template and include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Go version, etc.)
- Relevant logs or error messages

### Feature Requests

Use the feature request template and include:
- Clear description of the feature
- Use case and motivation
- Proposed implementation approach
- Potential impact on existing functionality

## Development Tips

### Debugging

```bash
# Enable debug logging
export LOG_LEVEL=debug
./fake-ollama

# Test specific endpoints
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-r1:14b", "messages": [{"role": "user", "content": "test"}]}'
```

### Performance Testing

```bash
# Load testing with curl
for i in {1..10}; do
  curl -X POST http://localhost:11434/api/chat \
    -H "Content-Type: application/json" \
    -d '{"model": "deepseek-r1:14b", "messages": [{"role": "user", "content": "test"}]}' &
done
wait
```

### Code Quality Tools

```bash
# Static analysis
go vet ./...
golangci-lint run

# Security scanning
gosec ./...

# Dependency checking
go mod tidy
go mod verify
```

## Questions and Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Search existing issues before creating new ones
- **Discussions**: Use GitHub Discussions for questions
- **Security**: Report security issues privately via email

Thank you for contributing to fake-ollama! 🚀
