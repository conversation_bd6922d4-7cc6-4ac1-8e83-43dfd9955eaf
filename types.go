package main

import "time"

// Request and response types for Ollama API compatibility

type ChatRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Stream   *bool     `json:"stream,omitempty"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// Chat completion delta structure for streaming responses
type ChatCompletionDelta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

type ChatCompletionChoice struct {
	Index        int                 `json:"index"`
	Delta        ChatCompletionDelta `json:"delta"`
	FinishReason *string             `json:"finish_reason"`
}

type ChatResponse struct {
	ID                string                 `json:"id"`
	Object            string                 `json:"object"`
	Created           int64                  `json:"created"`
	Model             string                 `json:"model"`
	SystemFingerprint string                 `json:"system_fingerprint"`
	Choices           []ChatCompletionChoice `json:"choices"`
}

type GenerateRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
	System string `json:"system,omitempty"`
	Stream *bool  `json:"stream,omitempty"`
	Raw    bool   `json:"raw,omitempty"`
	Format string `json:"format,omitempty"`
}

type GenerateResponse struct {
	Model           string        `json:"model"`
	CreatedAt       time.Time     `json:"created_at"`
	Response        string        `json:"response"`
	Done            bool          `json:"done"`
	Context         []int         `json:"context,omitempty"`
	TotalDuration   time.Duration `json:"total_duration,omitempty"`
	LoadDuration    time.Duration `json:"load_duration,omitempty"`
	PromptEvalCount int           `json:"prompt_eval_count,omitempty"`
	EvalCount       int           `json:"eval_count,omitempty"`
	DoneReason      string        `json:"done_reason,omitempty"`
}

type PullRequest struct {
	Name     string `json:"name"`
	Stream   *bool  `json:"stream,omitempty"`
	Insecure bool   `json:"insecure,omitempty"`
}

type PullResponse struct {
	Status    string `json:"status"`
	Digest    string `json:"digest,omitempty"`
	Total     int64  `json:"total,omitempty"`
	Completed int64  `json:"completed,omitempty"`
}

type EmbedRequest struct {
	Model    string `json:"model"`
	Input    any    `json:"input"`
	Truncate *bool  `json:"truncate,omitempty"`
}

type EmbedResponse struct {
	Model      string      `json:"model"`
	Embeddings [][]float32 `json:"embeddings"`
}

type CreateRequest struct {
	Name     string `json:"name"`
	Path     string `json:"path"`
	Stream   *bool  `json:"stream,omitempty"`
	Insecure bool   `json:"insecure,omitempty"`
}

type ShowRequest struct {
	Name    string `json:"name"`
	System  string `json:"system,omitempty"`
	Model   string `json:"model,omitempty"`
	Verbose bool   `json:"verbose,omitempty"`
}

type ShowResponse struct {
	License    string         `json:"license"`
	System     string         `json:"system"`
	Template   string         `json:"template"`
	Parameters string         `json:"parameters"`
	ModelInfo  map[string]any `json:"model_info"`
	ModifiedAt time.Time      `json:"modified_at"`
}

type CopyRequest struct {
	Source      string `json:"source"`
	Destination string `json:"destination"`
}

type DeleteRequest struct {
	Name  string `json:"name"`
	Model string `json:"model,omitempty"`
}

// Error types moved from openai/middleware.go
type Error struct {
	Message string  `json:"message"`
	Type    string  `json:"type"`
	Param   any     `json:"param"`
	Code    *string `json:"code"`
}

type ErrorResponse struct {
	Error Error `json:"error"`
}

// Error creation function
func NewError(code int, message string) ErrorResponse {
	var etype string
	switch code {
	case 400:
		etype = "invalid_request_error"
	case 404:
		etype = "not_found_error"
	default:
		etype = "api_error"
	}

	return ErrorResponse{Error{Type: etype, Message: message}}
}
