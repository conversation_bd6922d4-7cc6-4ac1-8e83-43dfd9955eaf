# CLI Reference - fake-ollama

This document provides a comprehensive reference for the fake-ollama command-line interface.

## Overview

The fake-ollama CLI provides a powerful interface for configuring and managing the OpenRouter proxy server with support for multiple AI providers, API key management, and comprehensive configuration validation.

## Basic Usage

```bash
fake-ollama [OPTIONS] [COMMAND]
```

### Interactive Mode

When required parameters are missing, fake-o<PERSON><PERSON> automatically enters interactive mode to guide you through setup:

```bash
# Start without parameters - interactive mode activates
fake-ollama

# Skip interactive mode for scripting
fake-ollama --non-interactive --api-key your-key
```

For complete interactive mode documentation, see [Interactive CLI Guide](interactive-cli-guide.md).

## Commands

### Server Commands

#### Start Server (Default)
```bash
# Start with OpenRouter (default provider)
fake-ollama --api-key sk-or-v1-your-openrouter-key-here

# Start with custom configuration
fake-ollama --provider moonshot-direct --api-key sk-your-moonshot-key --port 8080
```

#### Dry Run
```bash
# Validate configuration without starting server
fake-ollama --dry-run

# Verbose dry run with detailed logging
fake-ollama --dry-run --verbose
```

### Configuration Commands

#### List Models
```bash
# Show all available model mappings
fake-ollama --list-models
```

#### API Key Management
```bash
# Store API key securely in shell profile
fake-ollama --set-api-key sk-or-v1-your-openrouter-key

# Verify API key validity
fake-ollama --verify-api-key

# Verify specific API key
fake-ollama --verify-api-key --api-key your-key
```

#### Information Commands
```bash
# Show version and feature information
fake-ollama --version

# Show comprehensive help
fake-ollama --help
```

## Options

### Provider Selection

#### `--provider <name>`
Select AI provider (default: openrouter)

**Supported Providers:**
- `openrouter` - OpenRouter (default) - Access to multiple AI models
- `moonshot-direct` - Direct Moonshot AI API - Kimi K2 models  
- `anthropic` - Direct Anthropic API - Claude models
- `openai` - Direct OpenAI API - GPT models

```bash
# Use OpenRouter (default)
fake-ollama --provider openrouter --api-key sk-or-v1-your-openrouter-key

# Use Moonshot AI directly for Kimi K2
fake-ollama --provider moonshot-direct --api-key moonshot-key

# Use Anthropic directly for Claude
fake-ollama --provider anthropic --api-key sk-ant-key
```

### API Key Management

#### `--api-key <key>`
Specify API key directly

```bash
fake-ollama --api-key sk-or-v1-your-openrouter-key
```

#### `--set-api-key <key>`
Store API key securely in shell profile

```bash
fake-ollama --set-api-key sk-or-v1-your-openrouter-key
```

#### `--verify-api-key`
Test API key validity before starting server

```bash
fake-ollama --verify-api-key
```

### Configuration Files

#### `--config <file>`
Specify custom configuration file path

```bash
fake-ollama --config /path/to/custom-config.json
```

#### `--models <file>`
Specify custom model mappings file (default: models.json)

```bash
fake-ollama --models /path/to/custom-models.json
```

### Server Configuration

#### `--host <address>`
Server bind address (default: 0.0.0.0)

```bash
# Bind to localhost only
fake-ollama --host 127.0.0.1

# Bind to specific interface
fake-ollama --host *************
```

#### `--port <port>`
Server port (default: 11434)

```bash
# Use custom port
fake-ollama --port 8080

# Use with custom host
fake-ollama --host 127.0.0.1 --port 3000
```

### Logging and Debugging

#### `--verbose`
Enable detailed logging

```bash
fake-ollama --verbose
```

#### `--dry-run`
Validate configuration without starting server

```bash
fake-ollama --dry-run --verbose
```

#### `--non-interactive`
Disable interactive prompts for scripted usage

```bash
# Fail immediately if required parameters missing
fake-ollama --non-interactive

# Use in CI/CD environments
fake-ollama --non-interactive --api-key "$API_KEY"
```

## Environment Variables

The CLI respects the following environment variables:

### API Keys
- `OPENROUTER_API_KEY` - OpenRouter API key
- `MOONSHOT_API_KEY` - Moonshot AI API key  
- `ANTHROPIC_API_KEY` - Anthropic API key
- `OPENAI_API_KEY` - OpenAI API key

### Server Configuration
- `OLLAMA_HOST` - Server address (overrides --host and --port)
- `GIN_MODE` - Gin framework mode (release, debug)

### Logging
- `LOG_LEVEL` - Log level (debug, info, warn, error)
- `ENABLE_REQUEST_LOG` - Enable request logging (true/false)
- `ENABLE_RESPONSE_LOG` - Enable response logging (true/false)

## Common Use Cases

### Kimi K2 Integration with VS Code

```bash
# 1. Set up OpenRouter with Kimi K2 access
fake-ollama --provider openrouter --api-key sk-or-v1-your-openrouter-key

# 2. Configure VS Code native Ollama integration:
#    - Set Ollama endpoint: http://localhost:11434
#    - Select model: kimi-k2:latest or kimi-k2:coding
```

### Direct Moonshot AI Access

```bash
# Use Moonshot AI directly (bypassing OpenRouter)
fake-ollama --provider moonshot-direct --api-key your-moonshot-key
```

### Development and Testing

```bash
# Test configuration without starting server
fake-ollama --dry-run --verbose

# Start with verbose logging for debugging
fake-ollama --verbose --api-key your-key

# Use custom port for development
fake-ollama --port 8080 --host 127.0.0.1
```

### Production Deployment

```bash
# Store API key securely
fake-ollama --set-api-key sk-or-v1-your-production-key

# Start server (API key loaded from environment)
fake-ollama

# Or with custom configuration
fake-ollama --config /etc/fake-ollama/config.json --models /etc/fake-ollama/models.json
```

## Error Handling

### Common Errors and Solutions

#### "No API key found"
```bash
# Solution: Set API key via flag or environment variable
fake-ollama --api-key your-key
# OR
export OPENROUTER_API_KEY=your-key
fake-ollama
```

#### "Model mappings file not found"
```bash
# Solution: Specify correct path or create models.json
fake-ollama --models /path/to/models.json
```

#### "API key verification failed"
```bash
# Solution: Check API key validity
fake-ollama --verify-api-key --api-key your-key
```

#### "Port already in use"
```bash
# Solution: Use different port
fake-ollama --port 8080
```

## Configuration Validation

The CLI performs comprehensive validation:

✓ **Provider validation** - Ensures selected provider is supported  
✓ **API key format** - Validates API key format for each provider  
✓ **File existence** - Checks that config and model files exist  
✓ **Network connectivity** - Tests API connectivity during verification  
✓ **Port availability** - Validates server configuration  

## Integration Examples

### curl Examples
```bash
# List available models
curl http://localhost:11434/api/tags

# Chat with Kimi K2
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "kimi-k2:latest",
    "messages": [
      {"role": "user", "content": "Write a Python function to sort a list"}
    ]
  }'

# Generate text with DeepSeek R1
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:14b",
    "prompt": "Explain quantum computing in simple terms"
  }'
```

### Python Client Example
```python
import requests

# Configure client to use fake-ollama
base_url = "http://localhost:11434"

# List models
response = requests.get(f"{base_url}/api/tags")
models = response.json()

# Chat completion
response = requests.post(f"{base_url}/api/chat", json={
    "model": "kimi-k2:coding",
    "messages": [
        {"role": "user", "content": "Write a binary search function"}
    ]
})
result = response.json()
```

## Advanced Configuration

### Custom Model Mappings

Create a custom `models.json` file:

```json
{
  "version": "1.0",
  "mappings": [
    {
      "ollama_name": "my-custom-model:latest",
      "openrouter_name": "provider/model-name",
      "provider": "custom",
      "context_length": 32768,
      "capabilities": ["text", "coding"],
      "description": "Custom model for specific use case"
    }
  ]
}
```

Use with CLI:
```bash
fake-ollama --models custom-models.json
```

### Multiple Provider Setup

```bash
# Terminal 1: OpenRouter on default port
fake-ollama --provider openrouter --api-key sk-or-key

# Terminal 2: Moonshot direct on different port  
fake-ollama --provider moonshot-direct --api-key moonshot-key --port 11435

# Terminal 3: Anthropic direct on another port
fake-ollama --provider anthropic --api-key sk-ant-key --port 11436
```

For more information, see the [Integration Guide](kimik2-integration-guide.md) and [API Reference](api-reference.md).
