# A MODERN, OPINIONATED STYLE & ENGINEERING GUIDE  
__For Go ≥1.22, targeting servers, streaming and “async”__

## 0.  Guiding Principles

- Simplicity beats cleverness.  
- The runtime is already “async”; write synchronous-looking code.  
- Reliability > raw throughput. Measure before you optimise.  
- Fail fast, propagate errors, surface traces.  
- Prefer composition over inheritance (Go has none), interfaces over structs. 


## 1.  Project Skeleton & Tooling  

```bash
/cmd/api        # small main.go, wire everything together
/internal       # non-importable business code
/service    # domain logic
/transport  # http, grpc, ws, etc.
/pkg            # reusable public packages
/api            # .proto, .http, AsyncAPI, GraphQL SDL
/configs
/migrations
Makefile
go.mod (module github.com/acme/service-X)
```

- Use `go 1.22` minimum – range-over-func, iterators, context.  
- linter: `golangci-lint` – enable `errcheck`, `gosec`, `govet`, `revive`.  
- formatter: `gofmt` + `gci` (import grouping).  
- code gen: `//go:generate` for protobuf, mockgen, oapi-codegen, asyncapi-codegen.  
- tests: `go test -race -shuffle=on -coverprofile=.cover`.  

## 2.  Style Rules (opinionated deltas from Google)  
**Naming**  
- No `Get`, no `Manager`, no `I` prefix interfaces.  
- Acronyms are all-caps (`HTTP`, not `Http`).  
- Private structs start with lower-case; public interfaces end with `er`.  

**Function signatures**  
```txt
// Good – explicit, synchronous, no naked returns
func (s *Store) CreateUser(ctx context.Context, u User) (User, error)
// Bad – out param, context stored in struct, magic numbers
func (s *Store) CreateUser(u *User) error
```

Error handling  
- Always wrap: `fmt.Errorf("create user: %w", err)`  
- Sentinel errors are versioned: `var ErrUserNotFound = errors.New("user not found")`  
- Never ignore `err` in `defer f.Close()`.

Context  
- First parameter, called `ctx`.  
- WithTimeout/WithDeadline must defer cancel immediately **unless** the function is long-lived (server).  
- Never store `context.Context` in structs, global vars or interfaces that outlive the request.

## 3.  Concurrency & Lifetime  
“Async” in Go = goroutines + channels + context cancellation.  
- Prefer synchronous APIs; let caller spawn goroutines.  
- When you *do* need internal concurrency, use a supervisor pattern:

```go
type Worker struct {
    ctx    context.Context
    cancel context.CancelCauseFunc
    wg     sync.WaitGroup
}

func New() *Worker {
    ctx, cancel := context.WithCancelCause(context.Background())
    return &Worker{ctx: ctx, cancel: cancel}
}

func (w *Worker) Start() {
    w.wg.Add(1)
    go func() {
        defer w.wg.Done()
        w.loop()
    }()
}

func (w *Worker) Stop(err error) {
    w.cancel(err)
    w.wg.Wait()
}
```

- Use `golang.org/x/sync/errgroup` for fan-out/in with cancellation.  
- Avoid unbounded `time.After` leaks; use `time.NewTimer` and `Stop`.  
- Always bound channels (`ch := make(chan T, N)`) – size = max in-flight.

## 4.  Servers  
4.1 HTTP  
- stdlib `net/http` is fine; wrap with `http.Server{ BaseContext: ... }`.  
- Use `chi` or `httprouter` for routing; avoid heavyweight frameworks.  
- Middleware order: logger → recoverer → trace-id → auth → gzip → handler.  

4.2 gRPC  
- `google.golang.org/grpc` + `protoc-gen-go-grpc`.  
- Interceptors: logging, validation, ratelimit, auth.  
- Always set `grpc.MaxRecvMsgSize`, `grpc.KeepaliveParams`.  

4.3 Graceful shutdown  
```go
srv := &http.Server{Addr: ":8080", Handler: handler}
go func() {
    if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
        log.Fatalf("http: %v", err)
    }
}()

<-interrupt
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()
srv.Shutdown(ctx)
```

## 5.  Streaming Patterns  
5.1 Server-Sent Events / WebSocket  
- Handlers must respect `req.Context().Done()` for client disconnect.  
- Use `github.com/gorilla/websocket`; always set read limit and pong handler.  
- Send side: `select { case <-ctx.Done(): return case clientCh <- msg: }`.  

5.2 gRPC Streaming  
- Separate goroutines for `Send` and `Recv`; coordinate via `errgroup`.  
- Example:

```go
func (s *srv) Chat(stream pb.Chat_ChatServer) error {
    ctx := stream.Context()
    g, ctx := errgroup.WithContext(ctx)

    g.Go(func() error { return recvLoop(ctx, stream) })
    g.Go(func() error { return sendLoop(ctx, stream) })

    return g.Wait()
}
```

5.3 AsyncAPI / NATS / Kafka  
- Generate types from AsyncAPI contract (`asyncapi-codegen`).  
- One goroutine per subscription with bounded channel to worker pool.  
- Use `nats.Context(ctx)` or `kafka.Reader.WithContext(ctx)` to cancel.  

## 6.  Observability  
- Tracing: OpenTelemetry – `otel.Tracer("service")`.  
- Metrics: Prometheus – `promauto` histograms for latency, counters for requests.  
- Logs: `slog` (1.21+) with JSON handler, always include trace-id.  
- Health & ready endpoints: `/-/healthz`, `/-/ready` (k8s).  

## 7.  Testing  
- Unit: table-driven with `cmp.Diff`.  
- Contract: `httpexpect` / `grpcurl`.  
- Load: `vegeta` or `k6` (CI gate: p99 < 250 ms @ 1k RPS).  
- Race: `go test -race` in CI (fail build on data race).  
- Flake: `go test -count=100` nightly.  

## 8.  Security  
- Use `gosec` in CI (severity = medium+).  
- Always `http.MaxBytesHandler`, `timeout_handler`.  
- Validate JWT with `github.com/golang-jwt/jwt/v5`.  
- Secrets via env vars or `os.ReadFile("/var/run/secrets/...")` (k8s).  

## 9.  Dependency & Lifecycle Management  
- Pin major versions in `go.mod`; use `go mod tidy -diff` in CI.  
- Use `github.com/vektra/mockery` for interface mocks.  
- Automated upgrades with `dependabot` + weekly pipeline.  

## 10.  Makefile (opinionated)
```make
.PHONY: lint test build image dev
lint:
    golangci-lint run ./...
test:
    go test -race -shuffle=on -coverprofile=.cover ./...
build:
    go build -ldflags="-s -w -X main.version=$(shell git describe --tags)" -o bin/api ./cmd/api
image:
    docker build -t acme/service-x:$(shell git rev-parse --short HEAD) .
dev:
    air # live-reload via github.com/cosmtrek/air
```

## 11.  Example Skeleton
```go
func main() {
    cfg := mustLoadConfig()

    logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
    slog.SetDefault(logger)

    ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt)
    defer stop()

    db := mustConnectDB(ctx, cfg.DSN)
    defer db.Close()

    svc := service.New(db)
    srv := transport.NewHTTPServer(svc)

    go func() {
        if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
            logger.Error("server exit", "err", err)
            os.Exit(1)
        }
    }()

    <-ctx.Done()
    shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    if err := srv.Shutdown(shutdownCtx); err != nil {
        logger.Error("shutdown", "err", err)
    }
}
```

## 12.  Final Checklist  
- [ ] All public APIs take `ctx` and return `(T, error)`  
- [ ] All goroutines have explicit shutdown path  
- [ ] All I/O uses `context.WithTimeout`  
- [ ] `go test -race` passes  
- [ ] `golangci-lint` passes  
- [ ] `/metrics` + `/healthz` exported  
- [ ] Dockerfile uses `scratch` or `distroless`, non-root user  
- [ ] Secrets never committed  

__Ship it.__

---