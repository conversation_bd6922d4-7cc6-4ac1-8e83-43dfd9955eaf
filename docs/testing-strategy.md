# Testing Strategy for OpenRouter Proxy

## Overview
Comprehensive testing strategy to ensure the OpenRouter proxy maintains 100% Ollama API compatibility while delivering authentic AI responses.

## Testing Pyramid

```
    /\     E2E Tests (10%)
   /  \    Integration with real clients
  /____\   Full workflow validation
 /      \  
/        \  Integration Tests (30%)
\        /  OpenRouter API integration
 \______/   Request/response transformation
/        \  
\        /  Unit Tests (60%)
 \______/   Individual components
            Model mapping, utilities
```

## Unit Tests (60% of test suite)

### Core Components

**Model Mapping Tests**
```go
func TestModelMapping(t *testing.T) {
    tests := []struct {
        name           string
        ollamaModel    string
        expectedOR     string
        expectedError  bool
    }{
        {
            name:        "valid deepseek model",
            ollamaModel: "deepseek-r1:14b",
            expectedOR:  "deepseek/deepseek-r1",
        },
        {
            name:          "invalid model",
            ollamaModel:   "nonexistent:1b",
            expectedError: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mapping, err := getModelMapping(tt.ollamaModel)
            if tt.expectedError {
                assert.Error(t, err)
                return
            }
            assert.NoError(t, err)
            assert.Equal(t, tt.expectedOR, mapping.OpenRouterName)
        })
    }
}
```

**Request Transformation Tests**
```go
func TestOllamaToOpenRouterTransform(t *testing.T) {
    ollamaReq := &ChatRequest{
        Model: "deepseek-r1:14b",
        Messages: []Message{
            {Role: "user", Content: "Hello"},
        },
        Stream: &[]bool{false}[0],
    }
    
    orReq, err := transformOllamaToOpenRouter(ollamaReq)
    assert.NoError(t, err)
    assert.Equal(t, "deepseek/deepseek-r1", orReq.Model)
    assert.Equal(t, "user", orReq.Messages[0].Role)
    assert.Equal(t, "Hello", orReq.Messages[0].Content)
}
```

**Response Transformation Tests**
```go
func TestOpenRouterToOllamaTransform(t *testing.T) {
    orResp := &OpenRouterChatResponse{
        ID:      "chatcmpl-123",
        Object:  "chat.completion",
        Created: 1234567890,
        Model:   "deepseek/deepseek-r1",
        Choices: []OpenRouterChoice{
            {
                Index: 0,
                Message: OpenRouterMessage{
                    Role:    "assistant",
                    Content: "Hello there!",
                },
                FinishReason: "stop",
            },
        },
    }
    
    ollamaResp := transformOpenRouterToOllama(orResp, "deepseek-r1:14b")
    assert.Equal(t, "deepseek-r1:14b", ollamaResp.Model)
    assert.Equal(t, "assistant", ollamaResp.Choices[0].Delta.Role)
    assert.Equal(t, "Hello there!", ollamaResp.Choices[0].Delta.Content)
}
```

### Utility Functions

**Configuration Tests**
```go
func TestConfigurationLoading(t *testing.T) {
    // Test environment variable loading
    os.Setenv("OPENROUTER_API_KEY", "test-key")
    config := loadConfiguration()
    assert.Equal(t, "test-key", config.APIKey)
    
    // Test model mapping file loading
    config.ModelMappingFile = "testdata/models.json"
    mappings, err := loadModelMappings(config)
    assert.NoError(t, err)
    assert.Greater(t, len(mappings), 0)
}
```

**Error Handling Tests**
```go
func TestErrorHandling(t *testing.T) {
    tests := []struct {
        name           string
        orError        error
        expectedStatus int
        expectedMsg    string
    }{
        {
            name:           "rate limit error",
            orError:        &OpenRouterError{Code: 429, Message: "Rate limited"},
            expectedStatus: 429,
            expectedMsg:    "Rate limited",
        },
        {
            name:           "invalid model error",
            orError:        &OpenRouterError{Code: 404, Message: "Model not found"},
            expectedStatus: 404,
            expectedMsg:    "Model not found",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            status, msg := handleOpenRouterError(tt.orError)
            assert.Equal(t, tt.expectedStatus, status)
            assert.Contains(t, msg, tt.expectedMsg)
        })
    }
}
```

## Integration Tests (30% of test suite)

### OpenRouter API Integration

**Real API Tests** (with test API key)
```go
func TestOpenRouterIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test in short mode")
    }
    
    client := NewOpenRouterClient(testAPIKey)
    
    req := &OpenRouterChatRequest{
        Model: "openai/gpt-4o-mini",
        Messages: []OpenRouterMessage{
            {Role: "user", Content: "Say hello"},
        },
    }
    
    resp, err := client.ChatCompletion(context.Background(), req)
    assert.NoError(t, err)
    assert.NotEmpty(t, resp.Choices)
    assert.NotEmpty(t, resp.Choices[0].Message.Content)
}
```

**Streaming Tests**
```go
func TestStreamingIntegration(t *testing.T) {
    client := NewOpenRouterClient(testAPIKey)
    
    req := &OpenRouterChatRequest{
        Model:  "openai/gpt-4o-mini",
        Stream: true,
        Messages: []OpenRouterMessage{
            {Role: "user", Content: "Count to 5"},
        },
    }
    
    stream, err := client.ChatCompletionStream(context.Background(), req)
    assert.NoError(t, err)
    defer stream.Close()
    
    var chunks []string
    for {
        chunk, err := stream.Recv()
        if err == io.EOF {
            break
        }
        assert.NoError(t, err)
        if len(chunk.Choices) > 0 {
            chunks = append(chunks, chunk.Choices[0].Delta.Content)
        }
    }
    
    assert.Greater(t, len(chunks), 0)
}
```

### Proxy Integration Tests

**Full Request Flow Tests**
```go
func TestProxyRequestFlow(t *testing.T) {
    // Setup test server
    server := setupTestServer()
    defer server.Close()
    
    // Test chat endpoint
    reqBody := `{
        "model": "deepseek-r1:14b",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": false
    }`
    
    resp, err := http.Post(server.URL+"/api/chat", "application/json", strings.NewReader(reqBody))
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
    
    var chatResp ChatResponse
    err = json.NewDecoder(resp.Body).Decode(&chatResp)
    assert.NoError(t, err)
    assert.Equal(t, "deepseek-r1:14b", chatResp.Model)
    assert.NotEmpty(t, chatResp.Choices[0].Delta.Content)
}
```

**Fallback Mechanism Tests**
```go
func TestFallbackToMock(t *testing.T) {
    // Configure with invalid API key to trigger fallback
    os.Setenv("OPENROUTER_API_KEY", "invalid-key")
    os.Setenv("FALLBACK_TO_MOCK", "true")
    
    server := setupTestServer()
    defer server.Close()
    
    reqBody := `{
        "model": "deepseek-r1:14b",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": false
    }`
    
    resp, err := http.Post(server.URL+"/api/chat", "application/json", strings.NewReader(reqBody))
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
    
    // Should receive mock response
    var chatResp ChatResponse
    err = json.NewDecoder(resp.Body).Decode(&chatResp)
    assert.NoError(t, err)
    assert.Contains(t, chatResp.Choices[0].Delta.Content, "fake-ollama")
}
```

## End-to-End Tests (10% of test suite)

### Real Client Integration

**Ollama Python Client Test**
```python
import ollama
import pytest

def test_ollama_python_client():
    """Test with real ollama-python client"""
    client = ollama.Client(host='http://localhost:11434')
    
    response = client.chat(
        model='deepseek-r1:14b',
        messages=[
            {'role': 'user', 'content': 'What is 2+2?'}
        ]
    )
    
    assert 'message' in response
    assert 'content' in response['message']
    assert response['message']['content'].strip() != ""
    # Should not contain mock response indicators
    assert 'fake-ollama' not in response['message']['content']
```

**Streaming Client Test**
```python
def test_streaming_with_real_client():
    """Test streaming responses with real client"""
    client = ollama.Client(host='http://localhost:11434')
    
    chunks = []
    for chunk in client.chat(
        model='deepseek-r1:14b',
        messages=[{'role': 'user', 'content': 'Count to 3'}],
        stream=True
    ):
        chunks.append(chunk)
    
    assert len(chunks) > 1  # Should receive multiple chunks
    # Verify streaming format
    for chunk in chunks:
        assert 'message' in chunk
        assert 'content' in chunk['message']
```

### Performance Tests

**Load Testing**
```go
func TestConcurrentRequests(t *testing.T) {
    server := setupTestServer()
    defer server.Close()
    
    concurrency := 10
    requests := 50
    
    var wg sync.WaitGroup
    results := make(chan time.Duration, requests)
    
    for i := 0; i < requests; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            
            start := time.Now()
            resp, err := http.Post(server.URL+"/api/chat", "application/json", 
                strings.NewReader(`{"model": "deepseek-r1:14b", "messages": [{"role": "user", "content": "Hi"}]}`))
            duration := time.Since(start)
            
            assert.NoError(t, err)
            assert.Equal(t, http.StatusOK, resp.StatusCode)
            results <- duration
        }()
        
        if (i+1)%concurrency == 0 {
            wg.Wait()
        }
    }
    
    close(results)
    
    var totalDuration time.Duration
    count := 0
    for duration := range results {
        totalDuration += duration
        count++
        assert.Less(t, duration, 5*time.Second, "Request took too long")
    }
    
    avgDuration := totalDuration / time.Duration(count)
    t.Logf("Average response time: %v", avgDuration)
    assert.Less(t, avgDuration, 2*time.Second, "Average response time too high")
}
```

## Test Data Management

### Mock Data
```go
// testdata/mock_responses.json
{
  "chat_completion": {
    "id": "chatcmpl-test",
    "object": "chat.completion",
    "created": 1234567890,
    "model": "deepseek/deepseek-r1",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "This is a test response"
        },
        "finish_reason": "stop"
      }
    ]
  }
}
```

### Test Configuration
```yaml
# test-config.yaml
openrouter:
  base_url: "https://openrouter.ai/api/v1"
  api_key: "${TEST_OPENROUTER_API_KEY}"
  timeout: 30s

models:
  test_mappings:
    - ollama_name: "test:1b"
      openrouter_name: "openai/gpt-4o-mini"
      provider: "openai"
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v4
        with:
          go-version: '1.21'
      - run: go test -v -short ./...

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v4
        with:
          go-version: '1.21'
      - run: go test -v ./... -tags=integration
        env:
          TEST_OPENROUTER_API_KEY: ${{ secrets.TEST_OPENROUTER_API_KEY }}

  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - uses: actions/checkout@v4
      - name: Build and run server
        run: |
          go build -o fake-ollama
          ./fake-ollama &
          sleep 5
      - name: Run E2E tests
        run: |
          pip install ollama
          python -m pytest tests/e2e/
```

## Test Coverage Goals

- **Unit Tests**: >90% code coverage
- **Integration Tests**: All API endpoints covered
- **E2E Tests**: All major user workflows covered
- **Performance Tests**: Response time and concurrency benchmarks

## Quality Gates

### Pre-commit Checks
- All tests pass
- Code coverage >90%
- No linting errors
- Security scan passes

### Release Criteria
- All test suites pass
- Performance benchmarks met
- Manual testing completed
- Documentation updated
