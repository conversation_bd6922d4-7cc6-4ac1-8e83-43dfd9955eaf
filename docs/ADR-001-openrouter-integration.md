# ADR-001: OpenRouter Integration Architecture

## Status
Proposed

## Context
We need to transform fake-ollama from a mock response system into a production-ready proxy that routes requests to OpenRouter while maintaining complete Ollama API compatibility.

## Decision
We will implement a transparent proxy architecture that:

1. **Maintains Single Binary**: Keep the existing single-file Go application structure for simplicity
2. **Uses Environment-Based Configuration**: Configure OpenRouter integration via environment variables
3. **Implements Request/Response Transformation**: Transform between Ollama and OpenRouter API formats
4. **Provides Fallback Mechanisms**: Fall back to mock responses when OpenRouter is unavailable
5. **Preserves Streaming Behavior**: Maintain real-time streaming characteristics

## Rationale

### Architecture Decisions

**Single Binary Approach**
- **Pro**: Maintains deployment simplicity and existing Docker workflow
- **Pro**: Reduces operational complexity
- **Con**: Larger binary size, but acceptable for this use case

**Environment-Based Configuration**
- **Pro**: Follows 12-factor app principles
- **Pro**: Secure credential management
- **Pro**: Easy deployment across environments
- **Con**: Requires documentation for configuration options

**Transparent Proxy Pattern**
- **Pro**: Zero client-side changes required
- **Pro**: Maintains existing API contracts
- **Con**: More complex request/response handling

### Technical Decisions

**HTTP Client Choice: Go Standard Library**
```go
// Use standard http.Client with timeout and retry logic
client := &http.Client{
    Timeout: 30 * time.Second,
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
    },
}
```

**Model Mapping Strategy: Static Configuration with Override**
```go
type ModelMapping struct {
    OllamaName     string `json:"ollama_name"`
    OpenRouterName string `json:"openrouter_name"`
    Provider       string `json:"provider"`
    Description    string `json:"description"`
}
```

**Error Handling: Graceful Degradation**
- OpenRouter unavailable → Fall back to mock responses
- Invalid API key → Return clear error message
- Rate limiting → Implement exponential backoff

## Implementation Plan

### Phase 1: Core Integration
1. Add OpenRouter client with authentication
2. Implement basic request/response transformation
3. Add model mapping configuration

### Phase 2: Advanced Features
1. Streaming response handling
2. Error handling and fallback mechanisms
3. Request/response logging

### Phase 3: Production Readiness
1. Performance optimization
2. Monitoring and health checks
3. Security hardening

## Configuration Schema

```yaml
# Environment Variables
OPENROUTER_API_KEY: "sk-or-..."           # Required
OPENROUTER_BASE_URL: "https://openrouter.ai/api/v1"  # Optional
FALLBACK_TO_MOCK: "true"                  # Optional, default: true
LOG_LEVEL: "info"                         # Optional, default: info
MODEL_MAPPING_FILE: "./models.json"      # Optional

# Model Mapping File (models.json)
[
  {
    "ollama_name": "deepseek-r1:14b",
    "openrouter_name": "deepseek/deepseek-r1",
    "provider": "deepseek",
    "description": "DeepSeek R1 model for general purpose tasks"
  }
]
```

## Consequences

### Positive
- **Zero Breaking Changes**: Existing clients continue to work
- **Real AI Responses**: Users get authentic model responses
- **Flexible Configuration**: Easy to customize model mappings
- **Graceful Degradation**: Service remains available even with OpenRouter issues

### Negative
- **Increased Complexity**: More moving parts and potential failure points
- **External Dependency**: Reliance on OpenRouter service availability
- **Cost Implications**: Real API usage costs vs. free mock responses
- **Latency Overhead**: Additional network hop adds latency

### Risks and Mitigations

**Risk: OpenRouter API Changes**
- *Mitigation*: Version pinning, comprehensive testing, monitoring

**Risk: Rate Limiting**
- *Mitigation*: Implement request queuing, support multiple API keys

**Risk: Cost Overruns**
- *Mitigation*: Usage monitoring, configurable limits, cost alerts

**Risk: Performance Degradation**
- *Mitigation*: Connection pooling, caching, performance monitoring

## Alternatives Considered

### Alternative 1: Microservice Architecture
- **Rejected**: Adds operational complexity without significant benefits
- **Reason**: Single binary approach is simpler for this use case

### Alternative 2: Plugin System
- **Rejected**: Over-engineering for current requirements
- **Reason**: Environment-based configuration is sufficient

### Alternative 3: Direct OpenAI SDK Integration
- **Rejected**: Limits flexibility to other providers
- **Reason**: OpenRouter provides access to multiple providers

## References
- [OpenRouter API Documentation](https://openrouter.ai/docs)
- [Ollama API Specification](https://github.com/ollama/ollama/blob/main/docs/api.md)
- [12-Factor App Configuration](https://12factor.net/config)

## Review and Updates
- **Next Review**: After Phase 1 implementation
- **Update Trigger**: Major OpenRouter API changes or performance issues
