# Model Mapping Specification

## Overview
This document defines the mapping between Ollama model names and OpenRouter models, ensuring users can access real AI models through familiar Ollama naming conventions.

## Default Model Mappings

### DeepSeek R1 Series

| Ollama Model | OpenRouter Model | Provider | Context Length | Use Case |
|--------------|------------------|----------|----------------|----------|
| `deepseek-r1:14b` | `deepseek/deepseek-r1` | DeepSeek | 32K | General purpose, fast responses |
| `deepseek-r1:32b` | `openai/gpt-4o` | OpenAI | 128K | High-quality responses, complex tasks |
| `deepseek-r1:70b` | `anthropic/claude-3.5-sonnet` | Anthropic | 200K | Advanced reasoning, long context |
| `deepseek-r1:671b` | `openai/o1-preview` | OpenAI | 128K | Complex reasoning, research tasks |

### Additional Model Mappings (Future)

| Ollama Model | OpenRouter Model | Provider | Context Length | Use Case |
|--------------|------------------|----------|----------------|----------|
| `llama3:8b` | `meta-llama/llama-3-8b-instruct` | Meta | 8K | Fast, efficient responses |
| `llama3:70b` | `meta-llama/llama-3-70b-instruct` | Meta | 8K | High-quality open source |
| `codellama:13b` | `codellama/codellama-13b-instruct` | Meta | 16K | Code generation |
| `mistral:7b` | `mistralai/mistral-7b-instruct` | Mistral | 32K | Efficient multilingual |
| `gemma:7b` | `google/gemma-7b-it` | Google | 8K | Google's open model |

## Model Selection Criteria

### Performance Tiers
1. **Fast Tier** (14b): Optimized for speed, good quality
2. **Balanced Tier** (32b): Balance of speed and quality
3. **Quality Tier** (70b): High quality, advanced reasoning
4. **Premium Tier** (671b): Maximum capability, complex tasks

### Provider Selection Rationale

**DeepSeek → DeepSeek**
- Direct mapping maintains model family consistency
- Best performance for DeepSeek-specific optimizations

**32b → GPT-4o**
- GPT-4o provides excellent balance of speed and quality
- Wide compatibility and reliability

**70b → Claude 3.5 Sonnet**
- Superior reasoning capabilities
- Excellent for complex analysis and long-form content

**671b → O1-Preview**
- Advanced reasoning model for complex problems
- Best-in-class for research and analysis tasks

## Configuration Format

### JSON Configuration File (`models.json`)
```json
{
  "version": "1.0",
  "mappings": [
    {
      "ollama_name": "deepseek-r1:14b",
      "openrouter_name": "deepseek/deepseek-r1",
      "provider": "deepseek",
      "context_length": 32768,
      "cost_per_1k_tokens": {
        "input": 0.0014,
        "output": 0.0028
      },
      "capabilities": ["text", "reasoning"],
      "description": "DeepSeek R1 model for general purpose tasks",
      "fallback_models": ["openai/gpt-4o-mini"]
    },
    {
      "ollama_name": "deepseek-r1:32b",
      "openrouter_name": "openai/gpt-4o",
      "provider": "openai",
      "context_length": 128000,
      "cost_per_1k_tokens": {
        "input": 0.005,
        "output": 0.015
      },
      "capabilities": ["text", "reasoning", "function_calling"],
      "description": "GPT-4o for high-quality responses",
      "fallback_models": ["openai/gpt-4o-mini", "anthropic/claude-3-haiku"]
    }
  ]
}
```

### Environment Variable Override
```bash
# Override specific model mapping
OPENROUTER_MODEL_deepseek_r1_14b="anthropic/claude-3-haiku"
OPENROUTER_MODEL_deepseek_r1_32b="openai/gpt-4o-mini"
```

## Dynamic Model Discovery

### OpenRouter Model List Integration
```go
type OpenRouterModel struct {
    ID          string  `json:"id"`
    Name        string  `json:"name"`
    Description string  `json:"description"`
    Pricing     struct {
        Prompt     string `json:"prompt"`
        Completion string `json:"completion"`
    } `json:"pricing"`
    ContextLength int `json:"context_length"`
}

// Fetch available models from OpenRouter
func fetchAvailableModels() ([]OpenRouterModel, error) {
    // Implementation to fetch from /api/v1/models
}
```

### Auto-mapping Strategy
1. **Exact Name Match**: If Ollama model exists in OpenRouter, use directly
2. **Family Match**: Match by model family (e.g., llama3 → llama-3)
3. **Capability Match**: Match by capabilities and size
4. **Fallback**: Use configured default mapping

## Model Metadata Preservation

### Ollama Model Info Mapping
```go
type OllamaModelInfo struct {
    Model      string    `json:"model"`
    Name       string    `json:"name"`
    Size       int64     `json:"size"`
    Digest     string    `json:"digest"`
    ModifiedAt time.Time `json:"modified_at"`
    Details    struct {
        Format            string   `json:"format"`
        Family            string   `json:"family"`
        ParameterSize     string   `json:"parameter_size"`
        QuantizationLevel string   `json:"quantization_level"`
    } `json:"details"`
}

// Map OpenRouter model to Ollama format
func mapToOllamaModel(orModel OpenRouterModel, ollamaName string) OllamaModelInfo {
    // Implementation to preserve Ollama metadata format
}
```

## Cost Management

### Cost Tracking
- Track token usage per model
- Calculate costs based on OpenRouter pricing
- Provide usage reports and alerts

### Cost Optimization
- **Model Routing**: Route to cheaper models when appropriate
- **Caching**: Cache responses for repeated queries
- **Fallback Strategy**: Use cheaper models as fallbacks

## Testing Strategy

### Model Compatibility Testing
```go
func TestModelMapping(t *testing.T) {
    testCases := []struct {
        ollamaModel    string
        expectedOR     string
        expectedProvider string
    }{
        {"deepseek-r1:14b", "deepseek/deepseek-r1", "deepseek"},
        {"deepseek-r1:32b", "openai/gpt-4o", "openai"},
    }
    
    for _, tc := range testCases {
        mapping := getModelMapping(tc.ollamaModel)
        assert.Equal(t, tc.expectedOR, mapping.OpenRouterName)
        assert.Equal(t, tc.expectedProvider, mapping.Provider)
    }
}
```

### Response Quality Testing
- Compare response quality across different mappings
- Validate streaming behavior consistency
- Test error handling and fallback mechanisms

## Monitoring and Analytics

### Metrics to Track
- **Usage by Model**: Requests per model, tokens consumed
- **Performance**: Response times, error rates
- **Costs**: Spend per model, cost per request
- **Quality**: User satisfaction, response quality scores

### Alerting
- High error rates for specific models
- Unusual cost spikes
- Model availability issues

## Future Enhancements

### Planned Features
1. **Smart Routing**: Route based on query complexity
2. **A/B Testing**: Compare different model mappings
3. **User Preferences**: Allow users to specify preferred models
4. **Load Balancing**: Distribute load across multiple providers

### Model Expansion
- Add support for image generation models
- Include embedding models
- Support for fine-tuned models
- Multi-modal model support
