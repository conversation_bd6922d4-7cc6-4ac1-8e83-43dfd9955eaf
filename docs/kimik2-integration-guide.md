# Kimi K2 Integration Guide: VS Code Native Ollama + fake-ollama + OpenRouter

This guide shows how to integrate Moonshot AI's Kimi K2 model with VS Code's native Ollama support using the fake-ollama OpenRouter proxy, enabling access to this powerful 1T parameter coding model through familiar Ollama-compatible interfaces.

## Overview

**Kimi K2** is a 1-trillion parameter Mixture-of-Experts (MoE) model by Moonshot AI, specifically designed for:
- Advanced coding assistance (53.7% on LiveCodeBench)
- Agent capabilities and tool calling
- 128K context length for large codebases
- Multi-language support (English/Chinese)

**Integration Flow:**
```
VS Code (Native Ollama) → fake-ollama (localhost:11434) → OpenRouter API → Kimi K2 → Real AI Responses
```

## Prerequisites

1. **OpenRouter API Key**: Get one at [openrouter.ai/keys](https://openrouter.ai/keys)
2. **VS Code**: Latest version with native Ollama support
3. **fake-ollama**: Our OpenRouter proxy (this project)

## Step 1: Configure fake-ollama for Kimi K2

### 1.1 Update Model Mappings

Add Kimi K2 to your `models.json` configuration:

```json
{
  "version": "1.0",
  "default_provider": "openai",
  "fallback_model": "openai/gpt-4o-mini",
  "mappings": [
    {
      "ollama_name": "kimi-k2:latest",
      "openrouter_name": "moonshot/kimi-k2",
      "provider": "moonshot",
      "context_length": 128000,
      "cost_per_1k_tokens": {
        "input": "0.001",
        "output": "0.002"
      },
      "capabilities": ["text", "coding", "reasoning", "tool_calling"],
      "description": "Kimi K2 - 1T parameter MoE model for advanced coding",
      "fallback_models": ["openai/gpt-4o-mini"]
    },
    {
      "ollama_name": "kimi-k2:coding",
      "openrouter_name": "moonshot/kimi-k2",
      "provider": "moonshot",
      "context_length": 128000,
      "cost_per_1k_tokens": {
        "input": "0.001",
        "output": "0.002"
      },
      "capabilities": ["text", "coding", "reasoning", "tool_calling"],
      "description": "Kimi K2 optimized for coding tasks",
      "fallback_models": ["openai/gpt-4o-mini"]
    }
  ]
}
```

### 1.2 Set Environment Variables

```bash
# Required: Your OpenRouter API key
export OPENROUTER_API_KEY="sk-or-v1-your-api-key-here"

# Optional: Enhanced logging for debugging
export LOG_LEVEL="info"
export ENABLE_REQUEST_LOG="true"
export ENABLE_RESPONSE_LOG="true"

# Optional: Optimize for coding workloads
export REQUEST_TIMEOUT="60"  # Longer timeout for complex coding tasks
export MAX_RETRIES="3"
```

### 1.3 Start fake-ollama Server

```bash
# Build and start the server
go build -o fake-ollama
./fake-ollama

# You should see:
# 2025/01/22 10:30:45 Loaded X model mappings from models.json
# 2025/01/22 10:30:45 OpenRouter client initialized with base URL: https://openrouter.ai/api/v1
# 2025/01/22 10:30:45 Starting fake-ollama server on 0.0.0.0:11434...
```

### 1.4 Verify fake-ollama Setup

Test that Kimi K2 is available:

```bash
# List available models
curl http://localhost:11434/api/tags

# Test chat completion
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "kimi-k2:latest",
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ]
  }'
```

## Step 2: Configure VS Code Native Ollama Integration

### 2.1 Enable VS Code Ollama Support

VS Code has built-in support for Ollama models. Configure it to use fake-ollama:

1. **Open VS Code Settings:**
   - Press `Cmd/Ctrl + ,` to open Settings
   - Search for "ollama"

2. **Configure Ollama Endpoint:**
   - Set `ollama.endpoint` to: `http://localhost:11434`
   - Enable `ollama.enabled`: `true`

3. **Select Kimi K2 Model:**
   - In the model selector, choose: `kimi-k2:latest` or `kimi-k2:coding`

### 2.2 Alternative: GitHub Copilot Chat Integration

If you have GitHub Copilot, you can configure it to use Ollama:

1. **Open Copilot Chat Settings:**
   - Search for "copilot chat ollama" in settings
   - Set the Ollama endpoint to: `http://localhost:11434`

2. **Select Model:**
   - In Copilot Chat, use the model selector
   - Choose `kimi-k2:latest` or `kimi-k2:coding`

## Step 3: VS Code Settings Configuration

### 3.1 Update VS Code Settings

Add these settings to your VS Code `settings.json`:

```json
{
  "ollama.enabled": true,
  "ollama.endpoint": "http://localhost:11434",
  "ollama.model": "kimi-k2:latest",
  "ollama.maxTokens": 4096,
  "ollama.temperature": 0.1
}
```

### 3.2 Workspace-Specific Configuration

For project-specific settings, create `.vscode/settings.json`:

```json
{
  "ollama.model": "kimi-k2:coding",
  "ollama.temperature": 0.0
}
```

## Step 4: Testing the Integration

### 4.1 Verify Kimi K2 Models are Available

First, confirm that Kimi K2 models are loaded:

```bash
# Check available models
curl http://localhost:11434/api/tags | grep -i kimi

# You should see:
# "model": "kimi-k2:latest"
# "model": "kimi-k2:coding"
```

### 4.2 Basic Chat Test

1. Open VS Code's AI chat interface (or Copilot Chat if configured)
2. Send a message: "Hello, can you help me with Python coding?"
3. **With valid OpenRouter API key**: You'll get real Kimi K2 responses
4. **With test/invalid key**: You'll see `[FALLBACK]` responses (expected during testing)

### 4.3 Coding Assistant Test

1. Create a new Python file
2. Type a comment: `# Function to sort a list of dictionaries by a specific key`
3. Use VS Code's AI assistance to generate the function
4. With real API key, verify the quality and accuracy of Kimi K2's generated code

### 4.4 Complex Coding Task

Test Kimi K2's advanced capabilities:

```python
# Ask VS Code AI: "Create a Python class for a binary search tree with insert, search, and delete methods. Include proper error handling and documentation."
```

### 4.5 Verify Real vs Fallback Responses

- **Real Kimi K2 responses**: No `[FALLBACK]` prefix, high-quality coding assistance
- **Fallback responses**: Start with `[FALLBACK]`, indicate OpenRouter unavailable

## Step 5: Advanced Configuration

### 5.1 Custom System Prompts

Configure CodeGPT with coding-specific prompts:

```json
{
  "codegpt.systemPrompt": "You are Kimi K2, an expert coding assistant with 1T parameters. You excel at:\n- Writing clean, efficient code\n- Explaining complex algorithms\n- Debugging and optimization\n- Following best practices\n- Providing comprehensive documentation\n\nAlways explain your reasoning and provide working examples."
}
```

### 5.2 Performance Optimization

For better performance with large codebases:

```json
{
  "codegpt.maxTokens": 8192,
  "codegpt.temperature": 0.1,
  "codegpt.topP": 0.9,
  "codegpt.contextLength": 32768
}
```

## Troubleshooting

### Common Issues

**1. "Model not found" Error**
```bash
# Check if fake-ollama is running
curl http://localhost:11434/

# Verify model mapping
curl http://localhost:11434/api/tags | grep kimi
```

**2. Connection Refused**
```bash
# Ensure fake-ollama is running on correct port
netstat -an | grep 11434

# Check firewall settings
# Restart fake-ollama server
```

**3. Slow Responses**
- Increase `REQUEST_TIMEOUT` in environment variables
- Check OpenRouter API status
- Verify internet connection

**4. Fallback Responses**
If you see `[FALLBACK]` in responses:
- Check OpenRouter API key validity
- Verify model availability on OpenRouter
- Check fake-ollama logs for errors

### Debug Mode

Enable detailed logging:

```bash
export LOG_LEVEL="debug"
export ENABLE_REQUEST_LOG="true"
export ENABLE_RESPONSE_LOG="true"
./fake-ollama
```

### Alternative: Direct Moonshot AI Integration

If OpenRouter doesn't have Kimi K2, configure direct access:

1. Get API key from [platform.moonshot.ai](https://platform.moonshot.ai)
2. Use Custom provider in CodeGPT:
   - Endpoint: `https://api.moonshot.ai/v1/chat/completions`
   - Model: `kimi-k2-instruct`
   - API Key: Your Moonshot AI key

## Benefits of This Setup

1. **Real AI Responses**: Get authentic Kimi K2 responses, not mock data
2. **Ollama Compatibility**: Use familiar Ollama interfaces and tools
3. **Graceful Fallback**: Automatic fallback to mock responses if API unavailable
4. **Cost Effective**: Pay only for actual usage through OpenRouter
5. **Advanced Coding**: Access to 1T parameter model optimized for coding
6. **Large Context**: 128K context window for entire codebases
7. **Tool Calling**: Native support for function calling and agents

## Next Steps

- Explore Kimi K2's tool calling capabilities
- Set up custom coding workflows
- Integrate with other VS Code extensions
- Configure team-wide settings for consistent experience

For more information, see:
- [OpenRouter Documentation](https://openrouter.ai/docs)
- [CodeGPT Documentation](https://docs.codegpt.co)
- [Kimi K2 Official Repository](https://github.com/MoonshotAI/Kimi-K2)
