# Deployment Guide for OpenRouter Proxy

## Overview
This guide covers deployment options for the fake-ollama OpenRouter proxy, from development to production environments.

## Prerequisites

### Required
- Go 1.21 or later
- OpenRouter API key ([Get one here](https://openrouter.ai/keys))
- Docker (for containerized deployment)

### Optional
- Kubernetes cluster (for K8s deployment)
- Load balancer (for high availability)
- Monitoring stack (Prometheus, Grafana)

## Configuration

### Environment Variables

**Required Configuration**
```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY="sk-or-v1-..."           # Your OpenRouter API key (REQUIRED)

# Optional Configuration
OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"  # Default OpenRouter endpoint
OLLAMA_HOST="0.0.0.0:11434"                 # Server bind address
LOG_LEVEL="info"                            # Logging level (debug, info, warn, error)
MODEL_MAPPING_FILE="./models.json"         # Custom model mapping file
ENABLE_REQUEST_LOG="true"                   # Enable request logging
ENABLE_RESPONSE_LOG="true"                  # Enable response logging
REQUEST_TIMEOUT="30"                        # Request timeout in seconds
MAX_RETRIES="3"                             # Maximum retry attempts
```

**Advanced Configuration**
```bash
# Performance Tuning
MAX_CONCURRENT_REQUESTS="100"              # Maximum concurrent requests
REQUEST_TIMEOUT="30s"                      # Request timeout
OPENROUTER_TIMEOUT="25s"                   # OpenRouter API timeout

# Security
CORS_ORIGINS="*"                           # CORS allowed origins
RATE_LIMIT_REQUESTS="1000"                 # Requests per minute per IP
RATE_LIMIT_WINDOW="1m"                     # Rate limit window

# Monitoring
METRICS_ENABLED="true"                     # Enable Prometheus metrics
METRICS_PORT="9090"                        # Metrics server port
HEALTH_CHECK_PATH="/health"                # Health check endpoint
```

### Model Mapping Configuration

Create `models.json` for custom model mappings:
```json
{
  "version": "1.0",
  "default_provider": "openai",
  "fallback_model": "openai/gpt-4o-mini",
  "mappings": [
    {
      "ollama_name": "deepseek-r1:14b",
      "openrouter_name": "deepseek/deepseek-r1",
      "provider": "deepseek",
      "context_length": 32768,
      "cost_per_1k_tokens": {
        "input": 0.0014,
        "output": 0.0028
      },
      "fallback_models": ["openai/gpt-4o-mini"]
    }
  ]
}
```

## Deployment Options

### 1. Local Development

**Quick Start**
```bash
# Clone repository
git clone https://github.com/spoonnotfound/fake-ollama.git
cd fake-ollama

# Set required environment variables
export OPENROUTER_API_KEY="your-api-key-here"

# Optional: Set additional configuration
export LOG_LEVEL="info"
export ENABLE_REQUEST_LOG="true"

# Run directly
go run main.go

# Or build and run
go build -o fake-ollama
./fake-ollama
```

**With Custom Configuration**
```bash
# Create configuration
cat > .env << EOF
OPENROUTER_API_KEY=sk-or-v1-your-key
LOG_LEVEL=debug
ENABLE_REQUEST_LOG=true
ENABLE_RESPONSE_LOG=true
REQUEST_TIMEOUT=30
EOF

# Load environment and run
source .env
./fake-ollama
```

### 2. Docker Deployment

**Basic Docker Run**
```bash
# Pull latest image
docker pull ghcr.io/spoonnotfound/fake-ollama:latest

# Run with environment variables
docker run -d \
  --name fake-ollama \
  -p 11434:11434 \
  -e OPENROUTER_API_KEY="your-api-key" \
  -e FALLBACK_TO_MOCK="true" \
  ghcr.io/spoonnotfound/fake-ollama:latest
```

**Docker Compose**
```yaml
# docker-compose.yml
version: '3.8'

services:
  fake-ollama:
    image: ghcr.io/spoonnotfound/fake-ollama:latest
    ports:
      - "11434:11434"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - FALLBACK_TO_MOCK=true
      - LOG_LEVEL=info
    volumes:
      - ./models.json:/app/models.json:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: Add monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
```

**Run with Docker Compose**
```bash
# Create .env file
echo "OPENROUTER_API_KEY=your-api-key" > .env

# Start services
docker-compose up -d

# View logs
docker-compose logs -f fake-ollama

# Stop services
docker-compose down
```

### 3. Kubernetes Deployment

**Namespace and ConfigMap**
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: fake-ollama

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fake-ollama-config
  namespace: fake-ollama
data:
  models.json: |
    {
      "version": "1.0",
      "mappings": [
        {
          "ollama_name": "deepseek-r1:14b",
          "openrouter_name": "deepseek/deepseek-r1",
          "provider": "deepseek"
        }
      ]
    }
```

**Secret for API Key**
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: fake-ollama-secret
  namespace: fake-ollama
type: Opaque
data:
  openrouter-api-key: <base64-encoded-api-key>
```

**Deployment**
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fake-ollama
  namespace: fake-ollama
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fake-ollama
  template:
    metadata:
      labels:
        app: fake-ollama
    spec:
      containers:
      - name: fake-ollama
        image: ghcr.io/spoonnotfound/fake-ollama:latest
        ports:
        - containerPort: 11434
        env:
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: fake-ollama-secret
              key: openrouter-api-key
        - name: FALLBACK_TO_MOCK
          value: "true"
        - name: LOG_LEVEL
          value: "info"
        volumeMounts:
        - name: config
          mountPath: /app/models.json
          subPath: models.json
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 11434
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 11434
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: fake-ollama-config
```

**Service and Ingress**
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: fake-ollama-service
  namespace: fake-ollama
spec:
  selector:
    app: fake-ollama
  ports:
  - port: 11434
    targetPort: 11434
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fake-ollama-ingress
  namespace: fake-ollama
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: ollama.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fake-ollama-service
            port:
              number: 11434
```

**Deploy to Kubernetes**
```bash
# Apply all configurations
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n fake-ollama
kubectl get services -n fake-ollama

# View logs
kubectl logs -f deployment/fake-ollama -n fake-ollama

# Scale deployment
kubectl scale deployment fake-ollama --replicas=5 -n fake-ollama
```

## Production Considerations

### High Availability

**Load Balancing**
```nginx
# nginx.conf
upstream fake_ollama {
    server fake-ollama-1:11434;
    server fake-ollama-2:11434;
    server fake-ollama-3:11434;
}

server {
    listen 80;
    server_name ollama.yourdomain.com;
    
    location / {
        proxy_pass http://fake_ollama;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # Support streaming
        proxy_buffering off;
        proxy_cache off;
    }
}
```

**Health Checks**
```bash
# Health check script
#!/bin/bash
curl -f http://localhost:11434/health || exit 1
curl -f http://localhost:11434/api/tags || exit 1
```

### Security

**API Key Management**
```bash
# Use secrets management
export OPENROUTER_API_KEY=$(vault kv get -field=api_key secret/openrouter)

# Or use Kubernetes secrets
kubectl create secret generic openrouter-secret \
  --from-literal=api-key="your-api-key" \
  -n fake-ollama
```

**Network Security**
```yaml
# Network policy example
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: fake-ollama-netpol
  namespace: fake-ollama
spec:
  podSelector:
    matchLabels:
      app: fake-ollama
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 11434
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS to OpenRouter
```

### Monitoring

**Prometheus Configuration**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'fake-ollama'
    static_configs:
      - targets: ['fake-ollama:9090']
    metrics_path: /metrics
    scrape_interval: 10s
```

**Grafana Dashboard**
- Request rate and response times
- Error rates by endpoint
- OpenRouter API usage and costs
- Model usage statistics

### Backup and Recovery

**Configuration Backup**
```bash
# Backup configuration
kubectl get configmap fake-ollama-config -o yaml > backup/configmap.yaml
kubectl get secret fake-ollama-secret -o yaml > backup/secret.yaml

# Restore configuration
kubectl apply -f backup/
```

## Troubleshooting

### Common Issues

**API Key Issues**
```bash
# Test API key
curl -H "Authorization: Bearer $OPENROUTER_API_KEY" \
  https://openrouter.ai/api/v1/models

# Check logs for authentication errors
docker logs fake-ollama | grep -i "auth\|key\|401\|403"
```

**Connection Issues**
```bash
# Test connectivity to OpenRouter
curl -v https://openrouter.ai/api/v1/models

# Check DNS resolution
nslookup openrouter.ai

# Test from container
docker exec fake-ollama curl -v https://openrouter.ai/api/v1/models
```

**Performance Issues**
```bash
# Check resource usage
docker stats fake-ollama

# Monitor response times
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:11434/api/tags
```

### Logging and Debugging

**Enable Debug Logging**
```bash
export LOG_LEVEL=debug
./fake-ollama
```

**Log Analysis**
```bash
# Filter logs by level
docker logs fake-ollama 2>&1 | grep ERROR

# Monitor real-time logs
docker logs -f fake-ollama | grep -E "(ERROR|WARN|openrouter)"
```

## Maintenance

### Updates
```bash
# Pull latest image
docker pull ghcr.io/spoonnotfound/fake-ollama:latest

# Rolling update in Kubernetes
kubectl set image deployment/fake-ollama fake-ollama=ghcr.io/spoonnotfound/fake-ollama:latest -n fake-ollama
```

### Monitoring Health
```bash
# Check service health
curl http://localhost:11434/health

# Verify API functionality
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-r1:14b", "messages": [{"role": "user", "content": "test"}]}'
```
