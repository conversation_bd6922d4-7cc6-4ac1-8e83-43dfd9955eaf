# Code Hygiene & Architecture

## Engineering Directive – Code Hygiene & Size Control

This project enforces strict code hygiene standards to maintain readability, maintainability, and architectural integrity.

### 500-Line Maximum File Length

**Rule**: No file may exceed 500 physical lines (including comments and blanks).

**Rationale**: 
- Promotes focused, single-responsibility modules
- Improves code readability and navigation
- Reduces cognitive load for developers
- Encourages proper separation of concerns

### Enforcement

**Pre-commit Hook**: `.githooks/pre-commit`
- Automatically checks file sizes before commits
- Rejects commits that violate the 500-line limit
- Provides warnings for files approaching the limit (>400 lines)

**GitHub Actions**: `.github/workflows/go.yml`
- CI pipeline fails if any file exceeds 500 lines
- Runs on all pull requests and pushes to main

**Setup Pre-commit Hook**:
```bash
# Install the pre-commit hook
git config core.hooksPath .githooks

# Test the hook
.githooks/pre-commit
```

### Refactoring Guidelines

When a file approaches or exceeds the limit:

#### 1. **Extract Private Helpers**
- Move helpers >20 lines to separate files (`*Helpers.go`, `*Utils.go`)
- Extract functions used in ≥2 places

#### 2. **Split by Functional Responsibility**
- Separate concerns into cohesive modules
- Group related functionality together
- Maintain clear interfaces between modules

#### 3. **Apply DRY Principle**
- Remove code duplication
- Create reusable utility functions
- Consolidate similar patterns

#### 4. **Simplify and Inline**
- Inline single-use variables/functions
- Convert verbose imperative sequences into declarative helpers
- Use early returns and guard clauses to reduce nesting

### Current Architecture

The codebase has been refactored into focused modules:

```
fake-ollama/
├── main.go              (85 lines)  - Application entry point
├── config.go            (148 lines) - Configuration management
├── models.go            (118 lines) - Model definitions and utilities
├── types.go             (118 lines) - Request/response type definitions
├── utils.go             (95 lines)  - Utility functions and helpers
├── handlers.go          (240 lines) - Main API handlers
├── handlers_misc.go     (140 lines) - Miscellaneous API handlers
└── middleware.go        (105 lines) - Gin middleware functions
```

**Total**: 1,049 lines across 8 files (avg: 131 lines/file)
**Previous**: 1,154 lines in 1 file

### Benefits Achieved

✅ **Improved Readability**: Each file has a clear, focused purpose
✅ **Better Navigation**: Easy to find specific functionality
✅ **Reduced Complexity**: Smaller files are easier to understand
✅ **Enhanced Maintainability**: Changes are isolated to relevant modules
✅ **Cleaner Git History**: Changes affect only relevant files
✅ **Faster Code Reviews**: Reviewers can focus on specific areas

### Module Responsibilities

#### `main.go`
- Application initialization
- Server setup and routing
- Minimal, focused entry point

#### `config.go`
- Environment variable loading
- Configuration validation
- Model mapping file parsing
- Default configuration values

#### `models.go`
- Model metadata structures
- Model validation logic
- Model list creation
- Model timing initialization

#### `types.go`
- Request/response type definitions
- Error types and creation
- API compatibility structures
- Clean separation of data models

#### `utils.go`
- Language detection
- Text processing utilities
- Logging functions
- SSE helper functions

#### `handlers.go`
- Core API handlers (chat, generate, list)
- Main business logic
- Request validation and processing

#### `handlers_misc.go`
- Secondary API handlers (pull, embed, create, etc.)
- Utility endpoints
- Health checks and version info

#### `middleware.go`
- Gin middleware functions
- Request/response transformation
- OpenAI compatibility layer

### Quality Gates

**Pre-commit**:
- File size validation
- Syntax checking
- Import organization

**CI Pipeline**:
- Code hygiene validation
- Build verification
- Test execution
- Docker image creation

### Future Considerations

As the codebase grows, consider:

1. **Package Structure**: Split into multiple packages when logical
2. **Interface Definitions**: Extract interfaces for better testability
3. **Dependency Injection**: Improve modularity and testing
4. **Configuration Validation**: Enhanced validation and error reporting

### Monitoring

Track file sizes over time:
```bash
# Check current file sizes
wc -l *.go

# Monitor approaching limits
find . -name "*.go" -exec wc -l {} + | awk '$1 > 400 {print $2 ": " $1 " lines (approaching limit)"}'
```

### Violation Response

If a file exceeds 500 lines:

1. **Immediate**: Create refactor ticket
2. **Block**: Reject commit/PR until fixed
3. **Refactor**: Apply guidelines above
4. **Review**: Ensure new structure maintains functionality
5. **Test**: Verify all tests pass after refactoring

This approach ensures the codebase remains maintainable and scalable as it grows.
