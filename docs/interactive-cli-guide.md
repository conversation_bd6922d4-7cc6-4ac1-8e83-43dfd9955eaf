# Interactive CLI Guide - fake-ollama

This guide covers the interactive CLI mode that makes setting up fake-ollama user-friendly and error-resistant.

## Overview

The interactive CLI mode automatically activates when required configuration parameters are missing, guiding users through a step-by-step setup process with real-time validation and helpful error messages.

## When Interactive Mode Activates

Interactive mode triggers automatically when:
- No API key is provided (via `--api-key` flag or environment variable)
- `--non-interactive` flag is not set
- The application is running in a terminal (not scripted)

## Interactive Setup Flow

### Step 1: Provider Selection

```
🎯 Welcome to fake-ollama Interactive Setup
═══════════════════════════════════════════

Let's configure your AI proxy step by step.
You can exit anytime with Ctrl+C.

📡 Select AI Provider:
  1) OpenRouter - Access to multiple AI models (recommended)
  2) Moonshot AI - Direct access to Kimi K2 models
  3) Anthropic - Direct access to Claude models
  4) OpenAI - Direct access to GPT models

Enter your choice (1-4): 
```

**Features:**
- Numbered menu for easy selection
- Clear descriptions of each provider
- Input validation with retry mechanism
- Up to 3 attempts before cancellation

### Step 2: API Key Entry

```
🔑 Enter API Key for openrouter:
   Environment variable: OPENROUTER_API_KEY
   Expected format: sk-or-v1-...
   💡 Tip: You can paste your API key (Ctrl+V or Cmd+V)

(Paste your API key and press Enter - input will be hidden)
Key: ********
```

**Features:**
- **Paste-Friendly Input**: Optimized for pasting long API keys
- **Masked Input**: API keys hidden during entry (password-style)
- **Automatic Cleanup**: Removes extra whitespace, newlines, and tabs from pasted keys
- **Fallback Options**: Plain text input if masked input fails
- **Format Validation**: Real-time checking of provider-specific formats
- **Length Validation**: Ensures API keys meet minimum length requirements
- **API Connectivity Testing**: Live validation with progress indicator
- **Cross-Platform**: Works in terminal and non-terminal environments

### Step 3: Optional Configuration

```
⚙️  Optional Configuration:
Would you like to use a custom port? (default: 11434) (y/n): n
Would you like to use a custom host? (default: 0.0.0.0) (y/n): n
Enable verbose logging for debugging? (y/n): y
✅ Verbose logging enabled
```

**Features:**
- Optional port and host configuration
- Verbose logging toggle
- Smart defaults for quick setup
- Input validation for port numbers

### Step 4: Configuration Validation

```
🔍 Validating configuration...
Testing complete configuration...
✅ Loaded 6 model mappings
✅ Server will start on 0.0.0.0:11434
✅ Provider: openrouter
✅ Configuration saved successfully

🚀 Configuration complete! Starting server...
```

**Features:**
- Complete configuration testing
- Model mappings validation
- Automatic configuration saving
- Clear success indicators

## Error Handling and Retry Logic

### API Key Validation

The interactive mode performs comprehensive API key validation:

1. **Format Validation**: Checks provider-specific key formats and lengths
   - OpenRouter: `sk-or-v1-...` (minimum 50 characters)
   - OpenAI: `sk-...` (minimum 40 characters)
   - Anthropic: `sk-ant-...` (minimum 60 characters)
   - Moonshot: `sk-...` (minimum 30 characters, e.g., sk-0RbbR9nn2wZIe15VaOaykYWYAOAuFt9WNQpYrGN3eC5btl7V)

2. **Connectivity Testing**: Tests actual API connectivity
   ```
   🔍 Testing API key connectivity...
   ✅ API key validated successfully!
   ```

3. **Error Recovery**: On validation failure:
   ```
   ❌ Invalid API key format: API key too short (minimum 50 characters, got 25)
   💡 Make sure you copied the complete API key without extra spaces

   Would you like to try again? (1/3 attempts used) (y/n):
   ```

   **Paste Operation Failures**:
   ```
   ❌ Masked input failed: terminal error
   💡 Would you like to try entering the API key in plain text? (y/n): y
   Enter API key (will be visible): sk-or-v1-your-key-here
   ```

### Retry Mechanism

- **Maximum Attempts**: 3 tries per parameter
- **Clear Progress**: Shows attempt count (1/3, 2/3, 3/3)
- **User Choice**: Option to retry or cancel at each failure
- **Graceful Exit**: Helpful message when max attempts reached

### Graceful Exit Handling

When setup is cancelled or fails:

```
❌ Setup cancelled: API key validation cancelled

💡 You can also use non-interactive mode:
   fake-ollama --api-key your-key --provider openrouter
   fake-ollama --help  # for all options

🔧 Troubleshooting:
   • Get API keys: https://openrouter.ai/keys
   • Check network connectivity
   • Use --dry-run to test configuration
```

## Non-Interactive Mode

For scripted usage or CI/CD environments:

```bash
# Disable interactive prompts
fake-ollama --non-interactive --api-key your-key

# Will fail immediately if required parameters missing
fake-ollama --non-interactive
# Error: required parameters missing and non-interactive mode enabled
```

## Configuration Persistence

The interactive mode automatically saves validated configuration:

1. **API Key Storage**: Stores in shell profile (`.bashrc`, `.zshrc`, `.profile`)
2. **Environment Variables**: Sets appropriate variables for the selected provider
3. **Future Usage**: Subsequent runs will use saved configuration

Example saved configuration:
```bash
# fake-ollama API key
export OPENROUTER_API_KEY='sk-or-v1-your-validated-key'
```

## Integration with Existing CLI

Interactive mode works seamlessly with existing CLI flags:

### Skip Interactive for Provided Parameters

```bash
# Provider specified, only prompt for API key
fake-ollama --provider moonshot-direct

# API key specified, skip interactive entirely
fake-ollama --api-key your-key

# Both specified, no interactive prompts
fake-ollama --provider openrouter --api-key your-key
```

### Combine with Other Commands

```bash
# Interactive setup with dry-run
fake-ollama --dry-run
# (will prompt for missing config, then validate without starting)

# Interactive setup with verbose logging
fake-ollama --verbose
# (will prompt for config, then start with verbose output)
```

## Advanced Features

### Terminal Detection

The interactive mode intelligently detects the environment:

- **Terminal Environment**: Full interactive experience with masked input
- **Non-Terminal Environment**: Fallback to plain text input for compatibility
- **Piped Input**: Graceful handling of scripted input

### Progress Indicators

Real-time feedback during long operations:

```
🔍 Testing API key connectivity...
🔍 Testing API key connectivity.
🔍 Testing API key connectivity..
🔍 Testing API key connectivity...
✅ API key validated successfully!
```

### Provider-Specific Guidance

Tailored help for each provider:

- **OpenRouter**: Links to key generation, model availability
- **Moonshot**: Specific format requirements and documentation
- **Anthropic**: Claude-specific setup instructions
- **OpenAI**: GPT model access and billing information

## Common Use Cases

### First-Time Setup

```bash
# Just run without any parameters
./fake-ollama

# Interactive mode will guide through complete setup
# Result: Fully configured and ready to use
```

### Switching Providers

```bash
# Change from OpenRouter to Moonshot
./fake-ollama --provider moonshot-direct

# Will prompt for new API key, keep other settings
```

### Development and Testing

```bash
# Interactive setup with testing
./fake-ollama --dry-run

# Interactive setup with debugging
./fake-ollama --verbose
```

### CI/CD Integration

```bash
# Ensure no interactive prompts in automated environments
fake-ollama --non-interactive --api-key "$API_KEY" --provider openrouter
```

## Troubleshooting

### Common Issues

1. **"No terminal detected"**
   - Solution: Use `--non-interactive` flag for scripted usage

2. **"API key validation timeout"**
   - Solution: Check network connectivity, try again

3. **"Invalid API key format"** or **"API key too short"**
   - Solution: Ensure you copied the complete API key
   - Check provider documentation for correct format
   - Remove any extra spaces, newlines, or characters

4. **"Paste operation not working"**
   - Try using Ctrl+V (Windows/Linux) or Cmd+V (Mac)
   - If paste fails, choose plain text input option
   - Ensure your terminal supports paste operations

5. **"Masked input failed"**
   - Choose "yes" when prompted for plain text input
   - Copy the API key again and paste carefully
   - Check terminal compatibility

6. **"Maximum retry attempts reached"**
   - Solution: Use `--help` for non-interactive options
   - Try `--verify-api-key` to test your key first

### Debug Mode

Enable verbose logging during interactive setup:

```bash
# Start with verbose flag
fake-ollama --verbose

# Or enable during optional configuration step
# "Enable verbose logging for debugging? (y/n): y"
```

## Security Considerations

- **Masked Input**: API keys are never displayed in plain text
- **Secure Storage**: Keys stored in user's shell profile with appropriate permissions
- **No Logging**: API keys are not logged even in verbose mode
- **Memory Safety**: Keys cleared from memory after use

## Examples

### Complete Interactive Session

```bash
$ ./fake-ollama

🎯 Welcome to fake-ollama Interactive Setup
═══════════════════════════════════════════

Let's configure your AI proxy step by step.
You can exit anytime with Ctrl+C.

📡 Select AI Provider:
  1) OpenRouter - Access to multiple AI models (recommended)
  2) Moonshot AI - Direct access to Kimi K2 models
  3) Anthropic - Direct access to Claude models
  4) OpenAI - Direct access to GPT models

Enter your choice (1-4): 1
✅ Selected: OpenRouter (openrouter)

🔑 Enter API Key for openrouter:
   Environment variable: OPENROUTER_API_KEY
   Expected format: sk-or-v1-...
   Key: ********

🔍 Testing API key connectivity...
✅ API key validated successfully!

⚙️  Optional Configuration:
Would you like to use a custom port? (default: 11434) (y/n): n
Would you like to use a custom host? (default: 0.0.0.0) (y/n): n
Enable verbose logging for debugging? (y/n): y
✅ Verbose logging enabled

🔍 Validating configuration...
Testing complete configuration...
✅ Loaded 6 model mappings
✅ Server will start on 0.0.0.0:11434
✅ Provider: openrouter
✅ Configuration saved successfully

🚀 Configuration complete! Starting server...

🚀 fake-ollama v1.0.0
📡 Server: http://0.0.0.0:11434
🔗 Provider: openrouter (https://openrouter.ai/api/v1)
🤖 Models: 6 available
🔑 API Key: Configured (sk-or-v1...key)
```

The interactive CLI mode makes fake-ollama accessible to users of all technical levels while maintaining the power and flexibility of the command-line interface.
