package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

// OpenRouterClient handles communication with OpenRouter API
type OpenRouterClient struct {
	config     *Config
	httpClient *http.Client
	baseURL    string
}

// NewOpenRouterClient creates a new OpenRouter API client
func NewOpenRouterClient(config *Config) *OpenRouterClient {
	httpClient := &http.Client{
		Timeout: time.Duration(config.RequestTimeout) * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &OpenRouterClient{
		config:     config,
		httpClient: httpClient,
		baseURL:    config.OpenRouterBaseURL,
	}
}

// ChatCompletion sends a chat completion request to OpenRouter
func (c *OpenRouterClient) ChatCompletion(ctx context.Context, req OpenRouterChatRequest) (*OpenRouterChatResponse, error) {
	url := c.baseURL + "/chat/completions"
	
	// Ensure non-streaming for this method
	req.Stream = boolPtr(false)
	
	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(httpReq)

	if c.config.EnableRequestLog {
		log.Printf("[OPENROUTER REQUEST] %s %s", httpReq.Method, httpReq.URL.String())
	}

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if c.config.EnableResponseLog {
		log.Printf("[OPENROUTER RESPONSE] Status: %d", resp.StatusCode)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var response OpenRouterChatResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// ChatCompletionStream sends a streaming chat completion request to OpenRouter
func (c *OpenRouterClient) ChatCompletionStream(ctx context.Context, req OpenRouterChatRequest) (<-chan OpenRouterChatResponse, <-chan error) {
	responseChan := make(chan OpenRouterChatResponse, 10)
	errorChan := make(chan error, 1)

	go func() {
		defer close(responseChan)
		defer close(errorChan)

		url := c.baseURL + "/chat/completions"
		
		// Ensure streaming is enabled
		req.Stream = boolPtr(true)
		
		body, err := json.Marshal(req)
		if err != nil {
			errorChan <- fmt.Errorf("failed to marshal request: %w", err)
			return
		}

		httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
		if err != nil {
			errorChan <- fmt.Errorf("failed to create request: %w", err)
			return
		}

		c.setHeaders(httpReq)

		if c.config.EnableRequestLog {
			log.Printf("[OPENROUTER STREAM REQUEST] %s %s", httpReq.Method, httpReq.URL.String())
		}

		resp, err := c.httpClient.Do(httpReq)
		if err != nil {
			errorChan <- fmt.Errorf("request failed: %w", err)
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			errorChan <- c.handleErrorResponse(resp)
			return
		}

		// Process SSE stream
		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			select {
			case <-ctx.Done():
				return
			default:
				line := scanner.Text()
				
				// Skip empty lines and comments
				if line == "" || strings.HasPrefix(line, ":") {
					continue
				}
				
				// Parse SSE data
				if strings.HasPrefix(line, "data: ") {
					data := strings.TrimPrefix(line, "data: ")
					
					// Check for stream end
					if data == "[DONE]" {
						return
					}
					
					var streamResp OpenRouterChatResponse
					if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
						if c.config.LogLevel == "debug" {
							log.Printf("[OPENROUTER STREAM] Failed to parse: %s", data)
						}
						continue
					}
					
					responseChan <- streamResp
				}
			}
		}

		if err := scanner.Err(); err != nil {
			errorChan <- fmt.Errorf("stream reading error: %w", err)
		}
	}()

	return responseChan, errorChan
}

// TextCompletion sends a text completion request to OpenRouter
func (c *OpenRouterClient) TextCompletion(ctx context.Context, req OpenRouterCompletionRequest) (*OpenRouterCompletionResponse, error) {
	url := c.baseURL + "/completions"
	
	// Ensure non-streaming for this method
	req.Stream = boolPtr(false)
	
	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(httpReq)

	if c.config.EnableRequestLog {
		log.Printf("[OPENROUTER REQUEST] %s %s", httpReq.Method, httpReq.URL.String())
	}

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if c.config.EnableResponseLog {
		log.Printf("[OPENROUTER RESPONSE] Status: %d", resp.StatusCode)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var response OpenRouterCompletionResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// GetModels retrieves available models from OpenRouter
func (c *OpenRouterClient) GetModels(ctx context.Context) (*OpenRouterModelsResponse, error) {
	url := c.baseURL + "/models"

	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(httpReq)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var response OpenRouterModelsResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// setHeaders sets required headers for OpenRouter API requests
func (c *OpenRouterClient) setHeaders(req *http.Request) {
	req.Header.Set("Authorization", "Bearer "+c.config.OpenRouterAPIKey)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("HTTP-Referer", "https://github.com/spoonnotfound/fake-ollama")
	req.Header.Set("X-Title", "fake-ollama")
}

// handleErrorResponse processes error responses from OpenRouter
func (c *OpenRouterClient) handleErrorResponse(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read error response: %w", err)
	}

	var openRouterErr OpenRouterError
	if err := json.Unmarshal(body, &openRouterErr); err != nil {
		return fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	return fmt.Errorf("OpenRouter API error: %s", openRouterErr.Error.Message)
}

// Helper function to create bool pointer
func boolPtr(b bool) *bool {
	return &b
}
