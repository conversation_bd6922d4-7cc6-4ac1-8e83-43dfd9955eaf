package main

import (
	"log"
	"math/rand"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

// Initialization function
func init() {
	// Initialize random seed (deprecated but keeping for compatibility)
	rand.Seed(time.Now().UnixNano())

	// Initialize model modified times
	initializeModelTimes()
}

// Initialize configuration after CLI processing
func initializeConfig() {
	// Load configuration
	appConfig = loadConfig()

	// Only validate configuration if we have an API key
	apiKey := appConfig.OpenRouterAPIKey
	if apiKey != "" {
		validateConfig(appConfig)
	}

	// Load model mappings
	var err error
	modelMappings, err = loadModelMappings(appConfig.ModelMappingFile)
	if err != nil {
		log.Fatalf("Failed to load model mappings: %v", err)
	}

	// Update model times with loaded mappings
	updateModelTimesFromMappings()

	// Initialize OpenRouter client
	initOpenRouterClient()
}

func main() {
	// Process CLI arguments first
	if !processCLI() {
		// CLI command was executed, exit
		return
	}

	// Initialize configuration after CLI processing
	initializeConfig()

	// Set gin mode
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "" {
		ginMode = gin.ReleaseMode
	}
	gin.SetMode(ginMode)

	r := gin.Default()

	// Add global error handling middleware
	r.Use(func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("Panic recovered: %v", err)
				if !c.Writer.Written() {
					c.AbortWithStatus(500)
				}
			}
		}()
		c.Next()
	})

	// Set CORS
	r.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// Basic API routes with OpenRouter integration
	r.POST("/api/chat", chatHandlerWithOpenRouter)
	r.POST("/api/generate", generateHandlerWithOpenRouter)
	r.GET("/api/tags", ListHandler)
	r.POST("/api/pull", pullHandler)
	r.POST("/api/embed", embedHandler)
	r.POST("/api/embeddings", embedHandler) // Compatibility interface
	r.POST("/api/create", createHandler)
	r.POST("/api/show", showHandler)
	r.POST("/api/copy", copyHandler)
	r.DELETE("/api/delete", deleteHandler)
	r.POST("/api/delete", deleteHandler) // Compatibility interface

	// OpenAI compatibility routes
	r.POST("/v1/chat/completions", ChatMiddleware(), chatHandlerWithOpenRouter)
	r.POST("/v1/completions", CompletionsMiddleware(), generateHandlerWithOpenRouter)
	r.POST("/v1/embeddings", EmbeddingsMiddleware(), embedHandler)
	r.GET("/v1/models", ListMiddleware(), ListHandler)
	r.GET("/v1/models/:model", RetrieveMiddleware(), showHandler)

	// Health check and version info
	r.GET("/", healthHandler)
	r.HEAD("/", healthHandler)
	r.GET("/api/version", versionHandler)
	r.HEAD("/api/version", versionHandler)

	// Get service address configuration
	addr := os.Getenv("OLLAMA_HOST")
	if addr == "" {
		addr = "0.0.0.0:11434"
	}

	// Show startup information
	showStartupInfo(addr)

	// Start server
	log.Printf("Starting fake-ollama server on %s...", addr)
	if err := r.Run(addr); err != nil {
		log.Fatal(err)
	}
}
