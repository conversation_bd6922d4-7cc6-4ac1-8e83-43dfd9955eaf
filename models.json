{"version": "1.0", "default_provider": "openai", "fallback_model": "openai/gpt-4o-mini", "mappings": [{"ollama_name": "deepseek-r1:14b", "openrouter_name": "deepseek/deepseek-r1", "provider": "deepseek", "context_length": 32768, "cost_per_1k_tokens": {"input": "0.0014", "output": "0.0028"}, "capabilities": ["text", "reasoning"], "description": "DeepSeek R1 model for general purpose tasks", "fallback_models": ["openai/gpt-4o-mini"]}, {"ollama_name": "deepseek-r1:32b", "openrouter_name": "openai/gpt-4o", "provider": "openai", "context_length": 128000, "cost_per_1k_tokens": {"input": "0.005", "output": "0.015"}, "capabilities": ["text", "reasoning", "function_calling"], "description": "GPT-4o for high-quality responses and complex tasks", "fallback_models": ["openai/gpt-4o-mini", "anthropic/claude-3-haiku"]}, {"ollama_name": "deepseek-r1:70b", "openrouter_name": "anthropic/claude-3.5-sonnet", "provider": "anthropic", "context_length": 200000, "cost_per_1k_tokens": {"input": "0.003", "output": "0.015"}, "capabilities": ["text", "reasoning", "analysis", "long_context"], "description": "Claude 3.5 Sonnet for advanced reasoning and long context", "fallback_models": ["anthropic/claude-3-haiku", "openai/gpt-4o"]}, {"ollama_name": "deepseek-r1:671b", "openrouter_name": "openai/o1-preview", "provider": "openai", "context_length": 128000, "cost_per_1k_tokens": {"input": "0.015", "output": "0.060"}, "capabilities": ["text", "reasoning", "research", "complex_analysis"], "description": "OpenAI O1 Preview for complex reasoning and research tasks", "fallback_models": ["openai/gpt-4o", "anthropic/claude-3.5-sonnet"]}, {"ollama_name": "kimi-k2:latest", "openrouter_name": "moonshot/kimi-k2", "provider": "moonshot", "context_length": 128000, "cost_per_1k_tokens": {"input": "0.001", "output": "0.002"}, "capabilities": ["text", "coding", "reasoning", "tool_calling"], "description": "Kimi K2 - 1T parameter MoE model for advanced coding", "fallback_models": ["openai/gpt-4o-mini"]}, {"ollama_name": "kimi-k2:coding", "openrouter_name": "moonshot/kimi-k2", "provider": "moonshot", "context_length": 128000, "cost_per_1k_tokens": {"input": "0.001", "output": "0.002"}, "capabilities": ["text", "coding", "reasoning", "tool_calling"], "description": "Kimi K2 optimized for coding tasks", "fallback_models": ["openai/gpt-4o-mini"]}]}