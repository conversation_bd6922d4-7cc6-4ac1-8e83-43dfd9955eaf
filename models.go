package main

import (
	"fmt"
	"math/rand"
	"time"
)

// ModelDetails stores detailed information about models
type ModelDetails struct {
	Format            string    `json:"format,omitempty"`
	Family            string    `json:"family,omitempty"`
	Families          *[]string `json:"families"`
	ParameterSize     string    `json:"parameter_size,omitempty"`
	QuantizationLevel string    `json:"quantization_level,omitempty"`
}

// ListModelResponse represents a single model response
type ListModelResponse struct {
	Name       string       `json:"name"`
	Size       int64        `json:"size"`
	Digest     string       `json:"digest"`
	ModifiedAt time.Time    `json:"modified_at"`
	Details    ModelDetails `json:"details"`
}

// ListResponse represents the model list response
type ListResponse struct {
	Models  []ListModelResponse `json:"models"`
	Message string              `json:"message,omitempty"`
}

// OpenAI compatible response structures
type OpenAIModel struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	OwnedBy string `json:"owned_by"`
}

type OpenAIModelList struct {
	Object string        `json:"object"`
	Data   []OpenAIModel `json:"data"`
}

// Global variables for storing model modified times and configuration
var modelModifiedTimes map[string]time.Time
var appConfig *Config
var modelMappings map[string]ModelMapping

// Helper function to validate models using configuration
func isValidModel(model string) bool {
	_, exists := modelMappings[model]
	return exists
}

// Initialize model modified times
func initializeModelTimes() {
	baseTime := time.Now().UTC()
	modelModifiedTimes = make(map[string]time.Time)

	// Initialize with default models first (for backward compatibility)
	defaultModels := []string{
		"deepseek-r1:14b",
		"deepseek-r1:32b",
		"deepseek-r1:70b",
		"deepseek-r1:671b",
	}

	for _, model := range defaultModels {
		offset := time.Duration(rand.Int63n(24*60*60)) * time.Second
		modelModifiedTimes[model] = baseTime.Add(-offset)
	}
}

// Update model times with loaded mappings
func updateModelTimesFromMappings() {
	if modelMappings == nil {
		return
	}

	baseTime := time.Now().UTC()
	for ollamaName := range modelMappings {
		if _, exists := modelModifiedTimes[ollamaName]; !exists {
			offset := time.Duration(rand.Int63n(24*60*60)) * time.Second
			modelModifiedTimes[ollamaName] = baseTime.Add(-offset)
		}
	}
}

// Helper functions for dynamic model creation

// Estimate model size based on mapping information
func estimateModelSize(mapping ModelMapping) int64 {
	// Base size estimation on context length and provider
	baseSize := int64(**********) // 1GB base

	// Adjust based on context length
	contextMultiplier := int64(mapping.ContextLength) / 4096
	if contextMultiplier < 1 {
		contextMultiplier = 1
	}

	// Provider-specific adjustments
	switch mapping.Provider {
	case "moonshot":
		return baseSize * 15 * contextMultiplier // Kimi K2 is large
	case "openai":
		return baseSize * 20 * contextMultiplier // GPT-4 models
	case "anthropic":
		return baseSize * 25 * contextMultiplier // Claude models
	case "deepseek":
		return baseSize * 10 * contextMultiplier // DeepSeek models
	default:
		return baseSize * 8 * contextMultiplier
	}
}

// Generate a consistent digest for a model name
func generateModelDigest(modelName string) string {
	// Create a simple hash-like digest based on model name
	hash := 0
	for _, char := range modelName {
		hash = hash*31 + int(char)
	}

	// Convert to hex-like string
	return fmt.Sprintf("%x42c25d8c10a841bd24724309898ae851466696a7d7f3a0a408b895538ccbc%02x",
		hash&0xff, (hash>>8)&0xff)
}

// Get model family information based on provider and model name
func getModelFamilyInfo(mapping ModelMapping) (string, []string, string) {
	switch mapping.Provider {
	case "moonshot":
		return "kimi", []string{"kimi", "moonshot"}, "1T"
	case "openai":
		if mapping.OpenRouterName == "openai/gpt-4o" {
			return "gpt-4", []string{"gpt-4", "openai"}, "Unknown"
		}
		return "gpt", []string{"gpt", "openai"}, "Unknown"
	case "anthropic":
		return "claude", []string{"claude", "anthropic"}, "Unknown"
	case "deepseek":
		if mapping.OllamaName == "deepseek-r1:14b" {
			return "qwen2", []string{"qwen2"}, "14B"
		} else if mapping.OllamaName == "deepseek-r1:32b" {
			return "qwen2", []string{"qwen2"}, "32B"
		} else if mapping.OllamaName == "deepseek-r1:70b" {
			return "llama", []string{"llama"}, "70B"
		} else if mapping.OllamaName == "deepseek-r1:671b" {
			return "deepseek", []string{"deepseek"}, "671B"
		}
		return "deepseek", []string{"deepseek"}, "Unknown"
	default:
		return "unknown", []string{"unknown"}, "Unknown"
	}
}

// Create a model response from mapping configuration
func createModelResponse(ollamaName string, mapping ModelMapping) ListModelResponse {
	// Ensure model time exists
	if _, exists := modelModifiedTimes[ollamaName]; !exists {
		offset := time.Duration(rand.Int63n(24*60*60)) * time.Second
		modelModifiedTimes[ollamaName] = time.Now().UTC().Add(-offset)
	}

	// Generate model size based on provider and capabilities
	size := estimateModelSize(mapping)

	// Generate digest based on model name
	digest := generateModelDigest(ollamaName)

	// Determine model family and details based on provider
	family, families, paramSize := getModelFamilyInfo(mapping)

	return ListModelResponse{
		Name:       ollamaName,
		Size:       size,
		Digest:     digest,
		ModifiedAt: modelModifiedTimes[ollamaName],
		Details: ModelDetails{
			Format:            "gguf",
			Family:            family,
			Families:          &families,
			ParameterSize:     paramSize,
			QuantizationLevel: "Q4_K_M",
		},
	}
}

// Create model list for API responses
func createModelList() []ListModelResponse {
	var models []ListModelResponse

	// If model mappings are available, use them
	if modelMappings != nil && len(modelMappings) > 0 {
		for ollamaName, mapping := range modelMappings {
			models = append(models, createModelResponse(ollamaName, mapping))
		}
		return models
	}

	// Fallback to hardcoded models for backward compatibility
	qwen2Families := []string{"qwen2"}
	llamaFamilies := []string{"llama"}

	return []ListModelResponse{
		{
			Name:       "deepseek-r1:14b",
			Size:       **********, // ~9.0GB
			Digest:     "c42c25d8c10a841bd24724309898ae851466696a7d7f3a0a408b895538ccbc98",
			ModifiedAt: modelModifiedTimes["deepseek-r1:14b"],
			Details: ModelDetails{
				Format:            "gguf",
				Family:            "qwen2",
				Families:          &qwen2Families,
				ParameterSize:     "14B",
				QuantizationLevel: "Q4_K_M",
			},
		},
		{
			Name:       "deepseek-r1:32b",
			Size:       21474836480, // ~20GB
			Digest:     "d42c25d8c10a841bd24724309898ae851466696a7d7f3a0a408b895538ccbc99",
			ModifiedAt: modelModifiedTimes["deepseek-r1:32b"],
			Details: ModelDetails{
				Format:            "gguf",
				Family:            "qwen2",
				Families:          &qwen2Families,
				ParameterSize:     "32B",
				QuantizationLevel: "Q4_K_M",
			},
		},
		{
			Name:       "deepseek-r1:70b",
			Size:       46170898432, // ~43GB
			Digest:     "e42c25d8c10a841bd24724309898ae851466696a7d7f3a0a408b895538ccbc9a",
			ModifiedAt: modelModifiedTimes["deepseek-r1:70b"],
			Details: ModelDetails{
				Format:            "gguf",
				Family:            "llama",
				Families:          &llamaFamilies,
				ParameterSize:     "70B",
				QuantizationLevel: "Q4_K_M",
			},
		},
		{
			Name:       "deepseek-r1:671b",
			Size:       433791590400, // ~404GB
			Digest:     "f42c25d8c10a841bd24724309898ae851466696a7d7f3a0a408b895538ccbc9b",
			ModifiedAt: modelModifiedTimes["deepseek-r1:671b"],
			Details: ModelDetails{
				Format:            "gguf",
				Family:            "deepseek",
				Families:          nil, // Some models have null families like in real Ollama
				ParameterSize:     "671B",
				QuantizationLevel: "Q4_K_M",
			},
		},
	}
}
