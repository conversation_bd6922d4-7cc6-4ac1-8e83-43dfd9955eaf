name: Go

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  code-hygiene:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Check file size limits (500 lines max)
      run: |
        echo "🔍 Checking file sizes (500-line limit)..."
        violations=()
        for file in *.go; do
          if [ -f "$file" ]; then
            lines=$(wc -l < "$file")
            if [ "$lines" -gt 500 ]; then
              violations+=("$file: $lines lines (exceeds 500-line limit)")
            fi
          fi
        done

        if [ ${#violations[@]} -gt 0 ]; then
          echo "❌ Code hygiene violation: Files exceed 500-line limit"
          for violation in "${violations[@]}"; do
            echo "  • $violation"
          done
          echo ""
          echo "Please refactor these files into smaller, cohesive modules."
          exit 1
        fi

        echo "✅ All files within 500-line limit"

  cleanup:
    needs: code-hygiene
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Run cleanup action
      run: |
        chmod +x scripts/cleanup.sh
        export PATH=$PATH:$(go env GOPATH)/bin
        ./scripts/cleanup.sh

  documentation:
    needs: cleanup
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Run documentation update
      run: |
        chmod +x scripts/update-docs.sh
        ./scripts/update-docs.sh

    - name: Check for documentation changes
      run: |
        if [ -n "$(git status --porcelain docs/)" ]; then
          echo "❌ Documentation is out of date. Please run ./scripts/update-docs.sh and commit changes."
          git status --porcelain docs/
          exit 1
        else
          echo "✅ Documentation is up to date"
        fi

  build:
    needs: [code-hygiene, cleanup, documentation]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Build
      run: go build -v ./...

    - name: Test
      run: go test -v ./...

    - name: Log in to the Container registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata (tags, labels) for Docker
      if: github.event_name != 'pull_request'
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,format=long
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      if: github.event_name != 'pull_request'
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }} 